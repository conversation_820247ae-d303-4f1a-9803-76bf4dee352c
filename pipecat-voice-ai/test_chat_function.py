#!/usr/bin/env python3

"""
测试聊天功能
模拟真实的WebRTC连接和语音交互
"""

import asyncio
import json
import ssl
import aiohttp
from dotenv import load_dotenv

load_dotenv()

async def test_chat_functionality():
    """测试聊天功能"""
    print("🔍 测试pipecat语音机器人聊天功能...")
    
    # 创建SSL上下文，忽略证书验证
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    
    connector = aiohttp.TCPConnector(ssl=ssl_context)
    
    async with aiohttp.ClientSession(connector=connector) as session:
        # 1. 测试主页访问
        print("\n📡 测试主页访问...")
        try:
            async with session.get("https://su.guiyunai.fun:7860/") as response:
                print(f"✅ 主页响应: {response.status}")
        except Exception as e:
            print(f"❌ 主页访问失败: {e}")
            return False
        
        # 2. 测试客户端页面
        print("\n📱 测试客户端页面...")
        try:
            async with session.get("https://su.guiyunai.fun:7860/client/") as response:
                print(f"✅ 客户端页面响应: {response.status}")
                if response.status != 200:
                    print("❌ 客户端页面不可用")
                    return False
        except Exception as e:
            print(f"❌ 客户端页面访问失败: {e}")
            return False
        
        # 3. 测试WebRTC offer端点（模拟真实的SDP）
        print("\n🔗 测试WebRTC连接...")
        
        # 创建一个简化但有效的SDP offer
        mock_sdp = """v=0
o=- 4611731400430051336 2 IN IP4 127.0.0.1
s=-
t=0 0
a=group:BUNDLE 0
a=msid-semantic: WMS
m=audio 9 UDP/TLS/RTP/SAVPF 111 103 104 9 0 8 106 105 13 110 112 113 126
c=IN IP4 0.0.0.0
a=rtcp:9 IN IP4 0.0.0.0
a=ice-ufrag:4ZcD
a=ice-pwd:2/1muCWoOi3uLifh4MdwBHbx
a=ice-options:trickle
a=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00
a=setup:actpass
a=mid:0
a=sendrecv
a=rtcp-mux
a=rtpmap:111 opus/48000/2
a=rtcp-fb:111 transport-cc
a=fmtp:111 minptime=10;useinbandfec=1"""
        
        offer_data = {
            "pc_id": "test-connection-12345",
            "type": "offer",
            "sdp": mock_sdp
        }
        
        try:
            async with session.post(
                "https://su.guiyunai.fun:7860/api/offer",
                json=offer_data,
                timeout=30
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ WebRTC连接建立成功")
                    print(f"📡 连接ID: {result.get('pc_id', 'unknown')}")
                    print(f"📊 返回字段: {list(result.keys())}")
                    
                    # 检查返回的SDP answer
                    if 'sdp' in result and 'type' in result:
                        print("✅ 收到有效的SDP answer")
                        if result['type'] == 'answer':
                            print("✅ SDP类型正确")
                        else:
                            print(f"⚠️ SDP类型异常: {result['type']}")
                    else:
                        print("❌ SDP answer格式不完整")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ WebRTC连接失败: {response.status}")
                    print(f"错误详情: {error_text[:300]}...")
                    return False
        except Exception as e:
            print(f"❌ WebRTC连接测试失败: {e}")
            return False

async def test_api_services():
    """测试API服务"""
    print("\n🔍 测试后端API服务...")
    
    # 测试DeepSeek API
    print("\n🤖 测试DeepSeek API...")
    import os
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ 缺少DEEPSEEK_API_KEY")
        return False
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "user", "content": "你好，请简短回答：今天天气怎么样？"}
        ],
        "max_tokens": 50
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://api.deepseek.com/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    print(f"✅ DeepSeek API响应成功")
                    print(f"📝 AI回复: {content}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ DeepSeek API错误: {response.status}")
                    print(f"错误详情: {error_text[:200]}...")
                    return False
    except Exception as e:
        print(f"❌ DeepSeek API连接失败: {e}")
        return False

async def test_groq_stt():
    """测试Groq STT服务"""
    print("\n🎤 测试Groq STT服务...")
    
    import os
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        print("❌ 缺少GROQ_API_KEY")
        return False
    
    headers = {
        "Authorization": f"Bearer {api_key}",
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            # 测试模型列表端点
            async with session.get(
                "https://api.groq.com/openai/v1/models",
                headers=headers,
                timeout=10
            ) as response:
                if response.status == 200:
                    models = await response.json()
                    whisper_models = [m for m in models.get("data", []) if "whisper" in m.get("id", "").lower()]
                    if whisper_models:
                        print(f"✅ Groq STT可用，找到 {len(whisper_models)} 个Whisper模型")
                        for model in whisper_models[:2]:
                            print(f"   - {model.get('id')}")
                        return True
                    else:
                        print("⚠️ 未找到Whisper模型")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ Groq API错误: {response.status}")
                    print(f"错误详情: {error_text[:200]}...")
                    return False
    except Exception as e:
        print(f"❌ Groq STT测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试pipecat语音机器人聊天功能...")
    print("=" * 60)
    
    results = []
    
    # 测试基础功能
    results.append(await test_chat_functionality())
    
    # 测试API服务
    results.append(await test_api_services())
    
    # 测试STT服务
    results.append(await test_groq_stt())
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"✅ 成功: {sum(results)}/{len(results)}")
    print(f"❌ 失败: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有测试通过！聊天功能正常")
        print("💡 建议：")
        print("   1. 用手机访问 https://su.guiyunai.fun:7860")
        print("   2. 允许麦克风权限")
        print("   3. 点击Connect按钮")
        print("   4. 开始说话测试语音识别和AI回复")
    else:
        print("⚠️ 部分测试失败，需要检查配置")
        if not results[0]:
            print("   - WebRTC连接有问题")
        if not results[1]:
            print("   - DeepSeek API有问题")
        if not results[2]:
            print("   - Groq STT API有问题")
    
    return all(results)

if __name__ == "__main__":
    asyncio.run(main())
