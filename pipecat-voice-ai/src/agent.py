"""
LiveKit风水AI助手 - 官方架构 + DeepSeek集成
基于LiveKit Agents官方指南构建，替换OpenAI为DeepSeek
"""

import os
import asyncio
from dotenv import load_dotenv
from loguru import logger

from livekit import agents
from livekit.agents import AgentSession, Agent, RoomInputOptions
from livekit.plugins import (
    deepgram,
    cartesia,
    noise_cancellation,
    silero,
)
from livekit.plugins.turn_detector.multilingual import MultilingualModel

# 导入我们的DeepSeek LLM集成
from fengshui_llm import DeepSeekLLM
from utils.fengshui_knowledge import create_fengshui_functions

# 加载环境变量
load_dotenv()

# 配置日志
logger.add("logs/agent.log", rotation="1 day", retention="7 days", level="INFO")


class FengshuiAssistant(Agent):
    """风水AI助手 - 基于LiveKit官方架构"""
    
    def __init__(self) -> None:
        # 风水大师人设和指令
        master_name = os.getenv("FENGSHUI_MASTER_NAME", "张大师")
        expertise = os.getenv("FENGSHUI_EXPERTISE", "住宅风水,商业风水,墓地风水")
        
        instructions = f"""
你是{master_name}，一位拥有30年经验的专业风水大师。

🎯 **你的专长**：{expertise}

🗣️ **对话风格**：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释复杂的风水概念
- 结合传统风水理论和现代生活实际
- 给出具体可行的建议

📋 **服务内容**：
- 分析房屋布局和风水格局
- 提供家具摆放和装饰建议
- 解答风水相关问题
- 给出风水改善方案

💡 **回答原则**：
- 每次回答控制在200字以内，简洁有力
- 重点突出，避免过于复杂的术语
- 如需详细分析，可以分步骤说明
- 始终保持专业和友善的态度

请用中文与用户交流，提供专业的风水咨询服务。
"""
        
        super().__init__(instructions=instructions)
        logger.info(f"初始化风水助手: {master_name}")


async def entrypoint(ctx: agents.JobContext):
    """LiveKit Agent入口点 - 官方标准架构"""
    
    logger.info("🏮 启动LiveKit风水AI助手")
    
    # 检查必要的环境变量
    required_env_vars = [
        "DEEPSEEK_API_KEY",
        "DEEPGRAM_API_KEY", 
        "CARTESIA_API_KEY"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        logger.error(f"缺少必要的环境变量: {missing_vars}")
        raise ValueError(f"请设置环境变量: {', '.join(missing_vars)}")
    
    # 创建风水工具函数
    fengshui_functions = create_fengshui_functions()
    logger.info(f"加载了 {len(fengshui_functions)} 个风水工具函数")
    
    # 初始化AI组件 - 按照官方指南，但使用DeepSeek替代OpenAI
    try:
        # DeepSeek LLM (替代OpenAI)
        llm = DeepSeekLLM(
            model="deepseek-chat",
            temperature=0.7,
            max_tokens=2000
        )
        logger.info("✅ DeepSeek LLM初始化成功")
        
        # Deepgram STT (语音识别)
        stt = deepgram.STT(
            model="nova-2", 
            language="zh",  # 中文优化
            smart_format=True,
            punctuate=True
        )
        logger.info("✅ Deepgram STT初始化成功")
        
        # Cartesia TTS (语音合成)
        tts = cartesia.TTS(
            model="sonic-2",
            voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 可以选择中文声音
            language="zh"  # 中文语音
        )
        logger.info("✅ Cartesia TTS初始化成功")
        
        # Silero VAD (语音活动检测)
        vad = silero.VAD.load()
        logger.info("✅ Silero VAD初始化成功")
        
        # 多语言转换检测
        turn_detection = MultilingualModel()
        logger.info("✅ 多语言转换检测初始化成功")
        
    except Exception as e:
        logger.error(f"❌ AI组件初始化失败: {e}")
        raise
    
    # 创建Agent会话 - 官方标准架构
    session = AgentSession(
        stt=stt,
        llm=llm,
        tts=tts,
        vad=vad,
        turn_detection=turn_detection,
        # 添加风水工具函数
        fnc_ctx=agents.llm.FunctionContext().with_functions(fengshui_functions)
    )
    
    # 配置房间输入选项
    room_input_options = RoomInputOptions()
    
    # 如果使用LiveKit Cloud，启用噪音消除
    if os.getenv("LIVEKIT_CLOUD_API_KEY"):
        room_input_options.noise_cancellation = noise_cancellation.BVC()
        logger.info("✅ 启用LiveKit Cloud噪音消除")
    
    # 启动会话
    await session.start(
        room=ctx.room,
        agent=FengshuiAssistant(),
        room_input_options=room_input_options,
    )
    
    # 生成欢迎消息
    welcome_message = """
🏮 您好！我是您的专业风水顾问助手。

我可以为您提供：
• 房屋布局风水分析
• 家具摆放建议  
• 颜色搭配指导
• 风水改善方案

请告诉我您想咨询什么风水问题，或者描述一下您的房屋情况。
"""
    
    await session.generate_reply(
        instructions=f"用温和专业的语气说：{welcome_message}"
    )
    
    logger.info("🎯 风水AI助手已就绪，等待用户交互")


def main():
    """主函数 - 支持多种运行模式"""
    import sys
    
    # 设置日志级别
    log_level = os.getenv("LOG_LEVEL", "INFO")
    logger.remove()
    logger.add(sys.stderr, level=log_level)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "download-files":
            # 下载模型文件
            logger.info("📦 开始下载模型文件...")
            
            # 这里会自动下载Silero VAD和Turn Detector模型
            try:
                # 预加载模型以触发下载
                silero.VAD.load()
                MultilingualModel()
                logger.info("✅ 模型文件下载完成")
            except Exception as e:
                logger.error(f"❌ 模型文件下载失败: {e}")
                sys.exit(1)
            return
        
        elif command == "console":
            # 控制台模式 - 本地测试
            logger.info("🖥️ 启动控制台模式")
            
        elif command == "dev":
            # 开发模式 - 连接LiveKit服务器
            logger.info("🔧 启动开发模式")
            
        elif command == "start":
            # 生产模式
            logger.info("🚀 启动生产模式")
        
        else:
            logger.error(f"未知命令: {command}")
            logger.info("可用命令: download-files, console, dev, start")
            sys.exit(1)
    
    # 运行LiveKit Agent
    agents.cli.run_app(
        agents.WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=lambda: logger.info("🔥 预热风水AI助手...")
        )
    )


if __name__ == "__main__":
    main()
