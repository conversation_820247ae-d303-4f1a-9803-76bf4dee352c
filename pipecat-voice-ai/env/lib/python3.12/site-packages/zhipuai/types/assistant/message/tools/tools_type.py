
from typing import Union
from typing_extensions import Annotated, <PERSON><PERSON><PERSON><PERSON>

from .code_interpreter_delta_block import CodeInterpreter<PERSON>ool<PERSON><PERSON>
from .retrieval_delta_black import RetrievalTool<PERSON><PERSON>
from .web_browser_delta_block import <PERSON><PERSON>rowser<PERSON>ool<PERSON><PERSON>
from .....core._utils import PropertyInfo
from .drawing_tool_delta_block import Drawing<PERSON><PERSON><PERSON><PERSON>
from .function_delta_block import FunctionToolBlock

__all__ = ["ToolsType"]


ToolsType: TypeAlias = Annotated[
    Union[DrawingToolBlock, CodeInterpreterToolBlock, WebBrowserToolBlock, RetrievalToolBlock, FunctionToolBlock],
    PropertyInfo(discriminator="type"),
]