# 中文语音对话机器人使用指南

基于Pipecat框架的智能语音机器人，集成Groq AI + OpenAI STT + Groq TTS。

## 🚀 快速启动

### 1. 启动语音机器人

```bash
# 激活虚拟环境
source env/bin/activate

# 启动WebRTC模式（推荐）
python 01-say-one-thing.py --transport webrtc --host 0.0.0.0 --port 7860
```

### 2. 访问Web界面

打开浏览器访问: http://su.guiyunai.fun:7860

## 🎯 功能特性

### ✅ 已实现功能

1. **实时语音识别** - 使用OpenAI Whisper模型（通过Groq API）
2. **智能对话生成** - 使用Groq的Llama-3.1-8b-instant模型
3. **语音合成输出** - 使用Groq TTS服务
4. **WebRTC实时通信** - 基于Pipecat的WebRTC传输
5. **中文语言支持** - 全流程支持中文语音和文本
6. **语音活动检测** - 使用Silero VAD自动检测语音

### 🔧 技术架构

```
用户语音输入 → WebRTC传输 → Silero VAD → OpenAI STT → Groq LLM → Groq TTS → WebRTC传输 → 用户听到回复
```

**核心组件:**
- **传输层**: WebRTC (pipecat.transports)
- **语音识别**: OpenAI STT via Groq API
- **语言模型**: Groq Llama-3.1-8b-instant
- **语音合成**: Groq TTS
- **语音检测**: Silero VAD
- **管道框架**: Pipecat Pipeline

## 📋 使用步骤

### 1. 准备工作

确保已配置Groq API密钥：
```bash
# 检查.env文件
cat .env
```

应该包含：
```
# Groq API密钥 (必需，用于STT和LLM)
GROQ_API_KEY=********************************************************

# 智谱AI API密钥 (推荐，用于中文TTS)
ZHIPU_API_KEY=f0cb388b4b194279aef8897d4fa1bd95.PXSo4bqG2vNkbrF7

# TTS提供商选择 (可选，默认为zhipu)
TTS_PROVIDER=zhipu
```

### 2. 启动服务

```bash
# 方式1: WebRTC模式（推荐）
python 01-say-one-thing.py --transport webrtc --host 0.0.0.0 --port 7860

# 方式2: Daily.co模式（需要Daily API密钥）
python 01-say-one-thing.py --transport daily

# 方式3: Twilio模式
python 01-say-one-thing.py --transport twilio --host 0.0.0.0 --port 8000
```

### 3. 使用Web界面

1. 打开浏览器访问服务器地址
2. 点击"Connect"按钮连接到机器人
3. 允许浏览器访问麦克风权限
4. 开始说话，机器人会自动识别并回复

### 4. 语音交互流程

1. **连接建立**: 浏览器连接到WebRTC服务器
2. **欢迎消息**: 机器人播放中文欢迎语音
3. **语音输入**: 用户开始说话，VAD自动检测语音活动
4. **语音识别**: OpenAI Whisper将语音转换为中文文本
5. **AI处理**: Groq LLM生成中文回复
6. **语音合成**: 智谱AI TTS或Groq TTS将回复转换为语音
7. **语音输出**: 用户听到机器人的语音回复
8. **持续对话**: 重复步骤3-7，实现连续对话

## 🛠️ 配置选项

### 传输模式

- **webrtc**: 本地WebRTC服务器（无需外部API）
- **daily**: Daily.co WebRTC服务（需要Daily API密钥）
- **twilio**: Twilio WebSocket服务（需要Twilio配置）

### 服务配置

可以在代码中修改以下配置：

```python
# STT配置
stt = OpenAISTTService(
    model="whisper-large-v3",  # Whisper模型
    language=Language.ZH,      # 中文语言
)

# LLM配置
llm = OpenAILLMService(
    model="llama-3.1-8b-instant",  # Groq模型
)

# TTS配置 - 支持多种提供商
# 智谱AI TTS (推荐用于中文)
tts = ZhipuTTSService(
    api_key=os.getenv("ZHIPU_API_KEY"),
    voice_id="zh-CN-XiaoxiaoNeural",
    params=ZhipuTTSService.InputParams(
        language=Language.ZH,
        speed=1.0
    )
)

# 或者使用Groq TTS
tts = GroqTTSService(
    voice="alloy",  # 语音类型
)
```

## 🔍 故障排除

### 常见问题

1. **无法连接到服务器**
   - 检查防火墙设置，确保端口7860开放
   - 确认服务器正在运行

2. **语音识别不工作**
   - 检查浏览器麦克风权限
   - 确认Groq API密钥有效
   - 检查网络连接

3. **AI回复异常**
   - 检查Groq API配额
   - 查看服务器日志错误信息

4. **语音合成失败**
   - 确认Groq TTS服务可用
   - 检查API密钥权限

### 日志查看

服务器运行时会显示详细日志：
```
2025-07-31 17:28:11.882 | INFO | 🤖 启动中文语音对话机器人
2025-07-31 17:28:16.936 | INFO | Running example with SmallWebRTCTransport...
INFO: Uvicorn running on http://0.0.0.0:7860
```

### 性能优化

1. **降低延迟**:
   - 使用更快的Groq模型
   - 优化网络连接
   - 调整VAD敏感度

2. **提高质量**:
   - 使用更大的语言模型
   - 优化系统提示词
   - 调整TTS参数

## 📊 系统要求

- **Python**: 3.8+
- **内存**: 建议4GB+
- **网络**: 稳定的互联网连接
- **浏览器**: 支持WebRTC的现代浏览器
- **操作系统**: Linux/macOS/Windows

## 🔗 相关链接

- [Pipecat官方文档](https://docs.pipecat.ai/)
- [Groq API文档](https://console.groq.com/docs)
- [OpenAI Whisper文档](https://platform.openai.com/docs/guides/speech-to-text)

## 📝 开发说明

### 代码结构

```python
# 主要组件
- STT: OpenAI Whisper (via Groq)
- LLM: Groq Llama-3.1-8b-instant  
- TTS: Groq TTS
- Transport: WebRTC/Daily/Twilio
- VAD: Silero VAD
- Pipeline: Pipecat框架
```

### 扩展功能

可以通过修改Pipeline添加更多功能：
- 情感分析
- 多轮对话记忆
- 自定义语音风格
- 多语言支持

---

**享受与AI的中文语音对话！** 🎉
