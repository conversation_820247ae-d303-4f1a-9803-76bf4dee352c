#!/usr/bin/env python3

"""
测试DeepSeek API密钥
"""

import asyncio
import aiohttp
import os
from dotenv import load_dotenv

load_dotenv()

async def test_deepseek_api():
    """测试DeepSeek API"""
    print("🔍 测试DeepSeek API...")
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    print(f"🔑 使用API密钥: {api_key[:20]}...{api_key[-10:] if api_key else 'None'}")
    
    if not api_key:
        print("❌ 缺少DEEPSEEK_API_KEY")
        return False
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "user", "content": "你好，请用中文简短回答：今天天气怎么样？"}
        ],
        "max_tokens": 50
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print("📡 发送请求到DeepSeek API...")
            async with session.post(
                "https://api.deepseek.com/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            ) as response:
                print(f"📊 响应状态: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    print(f"✅ DeepSeek API响应成功")
                    print(f"📝 AI回复: {content}")
                    
                    # 显示更多信息
                    usage = result.get("usage", {})
                    if usage:
                        print(f"📈 Token使用: {usage}")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ DeepSeek API错误: {response.status}")
                    print(f"错误详情: {error_text}")
                    return False
    except Exception as e:
        print(f"❌ DeepSeek API连接失败: {e}")
        return False

async def test_deepseek_tts_capability():
    """测试DeepSeek是否支持TTS"""
    print("\n🔊 检查DeepSeek TTS能力...")
    
    # DeepSeek主要是LLM服务，通常不提供TTS
    print("ℹ️ DeepSeek主要提供LLM服务，不提供TTS功能")
    print("💡 建议TTS方案:")
    print("   1. OpenAI TTS (推荐)")
    print("   2. Azure TTS")
    print("   3. Google TTS")
    print("   4. 本地TTS解决方案")
    
    return False

async def main():
    """主函数"""
    print("🚀 开始测试DeepSeek API配置...")
    print("=" * 50)
    
    # 测试LLM功能
    llm_result = await test_deepseek_api()
    
    # 检查TTS能力
    tts_result = await test_deepseek_tts_capability()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"🤖 LLM功能: {'✅ 正常' if llm_result else '❌ 异常'}")
    print(f"🔊 TTS功能: {'✅ 支持' if tts_result else '❌ 不支持'}")
    
    if llm_result:
        print("\n🎉 DeepSeek API配置成功！")
        print("💡 建议:")
        print("   1. DeepSeek可以作为主要的LLM服务")
        print("   2. 需要配置额外的TTS服务实现语音输出")
        print("   3. 当前项目可以实现：语音识别 → DeepSeek对话 → 文本输出")
    else:
        print("\n⚠️ DeepSeek API配置有问题")
        print("🔧 请检查:")
        print("   1. API密钥是否正确")
        print("   2. 网络连接是否正常")
        print("   3. API配额是否充足")

if __name__ == "__main__":
    asyncio.run(main())
