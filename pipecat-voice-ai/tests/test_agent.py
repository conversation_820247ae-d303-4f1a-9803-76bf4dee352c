"""
LiveKit风水AI助手测试
"""

import pytest
import asyncio
import os
from unittest.mock import Mock, patch

# 设置测试环境变量
os.environ["DEEPSEEK_API_KEY"] = "test-key"
os.environ["DEEPGRAM_API_KEY"] = "test-key"
os.environ["CARTESIA_API_KEY"] = "test-key"

from src.fengshui_llm import DeepSeekLLM, FengshuiKnowledgeBase
from src.utils.fengshui_knowledge import FengshuiTools


class TestDeepSeekLLM:
    """测试DeepSeek LLM集成"""
    
    def test_init(self):
        """测试初始化"""
        llm = DeepSeekLLM(api_key="test-key")
        assert llm.model == "deepseek-chat"
        assert llm.api_key == "test-key"
        assert llm.temperature == 0.7
    
    def test_init_without_api_key(self):
        """测试没有API密钥的情况"""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="DEEPSEEK_API_KEY"):
                DeepSeekLLM()


class TestFengshuiKnowledgeBase:
    """测试风水知识库"""
    
    def test_get_direction_info(self):
        """测试方位信息获取"""
        info = FengshuiKnowledgeBase.get_direction_info("东")
        assert "东方属木" in info
        assert "健康" in info
        
        # 测试无效方位
        info = FengshuiKnowledgeBase.get_direction_info("无效方位")
        assert "请提供具体的方位信息" in info
    
    def test_get_room_advice(self):
        """测试房间建议获取"""
        advice = FengshuiKnowledgeBase.get_room_advice("客厅")
        assert "客厅是家庭的核心" in advice
        
        # 测试无效房间类型
        advice = FengshuiKnowledgeBase.get_room_advice("无效房间")
        assert "请提供具体的房间类型" in advice


class TestFengshuiTools:
    """测试风水工具函数"""
    
    def test_get_direction_analysis(self):
        """测试方位分析"""
        result = FengshuiTools.get_direction_analysis("东")
        assert "东方位风水分析" in result
        assert "五行属性" in result
        assert "木" in result
    
    def test_get_room_layout_advice(self):
        """测试房间布局建议"""
        result = FengshuiTools.get_room_layout_advice("客厅")
        assert "客厅风水布局建议" in result
        assert "核心原则" in result
        assert "沙发摆放" in result
    
    def test_get_color_fengshui(self):
        """测试颜色风水"""
        result = FengshuiTools.get_color_fengshui("红色")
        assert "红色的风水含义" in result
        assert "五行属性" in result
        assert "火" in result


@pytest.mark.asyncio
async def test_agent_components():
    """测试Agent组件集成"""
    
    # 模拟测试环境
    with patch('src.fengshui_llm.httpx.AsyncClient') as mock_client:
        # 模拟DeepSeek API响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.aiter_lines = Mock(return_value=iter([
            "data: {\"choices\":[{\"delta\":{\"content\":\"测试响应\"}}]}",
            "data: [DONE]"
        ]))
        
        mock_client.return_value.__aenter__.return_value.stream.return_value.__aenter__.return_value = mock_response
        
        # 创建LLM实例
        llm = DeepSeekLLM(api_key="test-key")
        
        # 这里可以添加更多的集成测试
        assert llm is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
