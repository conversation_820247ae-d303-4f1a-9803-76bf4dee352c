<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麦克风测试工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: rgba(46, 204, 113, 0.3); border: 1px solid #2ecc71; }
        .error { background: rgba(231, 76, 60, 0.3); border: 1px solid #e74c3c; }
        .info { background: rgba(52, 152, 219, 0.3); border: 1px solid #3498db; }
        .warning { background: rgba(241, 196, 15, 0.3); border: 1px solid #f1c40f; }
        
        .volume-meter {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .volume-bar {
            height: 100%;
            background: linear-gradient(90deg, #2ecc71, #f1c40f, #e74c3c);
            width: 0%;
            transition: width 0.1s ease;
        }
        
        #audioVisualizer {
            width: 100%;
            height: 100px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 麦克风功能测试</h1>
        
        <div class="test-section">
            <h3>1. 浏览器兼容性检查</h3>
            <div id="browserCheck"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 麦克风权限测试</h3>
            <button onclick="testMicrophonePermission()">请求麦克风权限</button>
            <div id="permissionStatus"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 音频输入测试</h3>
            <button onclick="startAudioTest()" id="audioTestBtn">开始音频测试</button>
            <button onclick="stopAudioTest()" id="stopAudioBtn" disabled>停止测试</button>
            <div id="audioStatus"></div>
            <div class="volume-meter">
                <div class="volume-bar" id="volumeBar"></div>
            </div>
            <canvas id="audioVisualizer"></canvas>
        </div>
        
        <div class="test-section">
            <h3>4. WebRTC连接测试</h3>
            <button onclick="testWebRTC()">测试WebRTC连接</button>
            <div id="webrtcStatus"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 语音机器人连接测试</h3>
            <button onclick="testVoiceBot()">连接语音机器人</button>
            <div id="voiceBotStatus"></div>
        </div>
    </div>

    <script>
        let audioContext;
        let microphone;
        let analyser;
        let dataArray;
        let animationId;
        
        // 1. 浏览器兼容性检查
        function checkBrowserCompatibility() {
            const checks = {
                'getUserMedia': !!navigator.mediaDevices?.getUserMedia,
                'WebRTC': !!window.RTCPeerConnection,
                'AudioContext': !!(window.AudioContext || window.webkitAudioContext),
                'HTTPS': location.protocol === 'https:'
            };
            
            let html = '';
            for (const [feature, supported] of Object.entries(checks)) {
                const status = supported ? 'success' : 'error';
                const icon = supported ? '✅' : '❌';
                html += `<div class="status ${status}">${icon} ${feature}: ${supported ? '支持' : '不支持'}</div>`;
            }
            
            document.getElementById('browserCheck').innerHTML = html;
        }
        
        // 2. 麦克风权限测试
        async function testMicrophonePermission() {
            const statusDiv = document.getElementById('permissionStatus');
            
            try {
                statusDiv.innerHTML = '<div class="status info">🔄 正在请求麦克风权限...</div>';
                
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    } 
                });
                
                statusDiv.innerHTML = '<div class="status success">✅ 麦克风权限已获取</div>';
                
                // 停止流以释放资源
                stream.getTracks().forEach(track => track.stop());
                
            } catch (error) {
                let errorMsg = '❌ 麦克风权限被拒绝';
                if (error.name === 'NotFoundError') {
                    errorMsg = '❌ 未找到麦克风设备';
                } else if (error.name === 'NotAllowedError') {
                    errorMsg = '❌ 麦克风权限被用户拒绝';
                } else if (error.name === 'NotReadableError') {
                    errorMsg = '❌ 麦克风被其他应用占用';
                }
                
                statusDiv.innerHTML = `<div class="status error">${errorMsg}<br>错误详情: ${error.message}</div>`;
            }
        }
        
        // 3. 音频输入测试
        async function startAudioTest() {
            const statusDiv = document.getElementById('audioStatus');
            const audioTestBtn = document.getElementById('audioTestBtn');
            const stopAudioBtn = document.getElementById('stopAudioBtn');
            
            try {
                statusDiv.innerHTML = '<div class="status info">🔄 启动音频测试...</div>';
                
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                // 创建音频上下文
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                microphone = audioContext.createMediaStreamSource(stream);
                analyser = audioContext.createAnalyser();
                
                analyser.fftSize = 256;
                const bufferLength = analyser.frequencyBinCount;
                dataArray = new Uint8Array(bufferLength);
                
                microphone.connect(analyser);
                
                statusDiv.innerHTML = '<div class="status success">✅ 音频测试已启动，请说话测试</div>';
                
                audioTestBtn.disabled = true;
                stopAudioBtn.disabled = false;
                
                // 开始可视化
                visualizeAudio();
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ 音频测试失败: ${error.message}</div>`;
            }
        }
        
        function stopAudioTest() {
            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }
            
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            
            document.getElementById('audioTestBtn').disabled = false;
            document.getElementById('stopAudioBtn').disabled = true;
            document.getElementById('audioStatus').innerHTML = '<div class="status info">🔄 音频测试已停止</div>';
            document.getElementById('volumeBar').style.width = '0%';
        }
        
        function visualizeAudio() {
            if (!analyser) return;
            
            analyser.getByteFrequencyData(dataArray);
            
            // 计算音量
            let sum = 0;
            for (let i = 0; i < dataArray.length; i++) {
                sum += dataArray[i];
            }
            const average = sum / dataArray.length;
            const volume = (average / 255) * 100;
            
            // 更新音量条
            document.getElementById('volumeBar').style.width = volume + '%';
            
            // 绘制频谱
            const canvas = document.getElementById('audioVisualizer');
            const ctx = canvas.getContext('2d');
            const width = canvas.width = canvas.offsetWidth;
            const height = canvas.height = canvas.offsetHeight;
            
            ctx.clearRect(0, 0, width, height);
            
            const barWidth = width / dataArray.length;
            let x = 0;
            
            for (let i = 0; i < dataArray.length; i++) {
                const barHeight = (dataArray[i] / 255) * height;
                
                const r = barHeight + 25 * (i / dataArray.length);
                const g = 250 * (i / dataArray.length);
                const b = 50;
                
                ctx.fillStyle = `rgb(${r},${g},${b})`;
                ctx.fillRect(x, height - barHeight, barWidth, barHeight);
                
                x += barWidth;
            }
            
            animationId = requestAnimationFrame(visualizeAudio);
        }
        
        // 4. WebRTC连接测试
        async function testWebRTC() {
            const statusDiv = document.getElementById('webrtcStatus');
            
            try {
                statusDiv.innerHTML = '<div class="status info">🔄 测试WebRTC连接...</div>';
                
                const pc = new RTCPeerConnection({
                    iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                });
                
                // 添加音频轨道
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => pc.addTrack(track, stream));
                
                // 创建offer
                const offer = await pc.createOffer();
                await pc.setLocalDescription(offer);
                
                statusDiv.innerHTML = '<div class="status success">✅ WebRTC连接测试成功</div>';
                
                // 清理资源
                stream.getTracks().forEach(track => track.stop());
                pc.close();
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ WebRTC连接测试失败: ${error.message}</div>`;
            }
        }
        
        // 5. 语音机器人连接测试
        async function testVoiceBot() {
            const statusDiv = document.getElementById('voiceBotStatus');
            
            try {
                statusDiv.innerHTML = '<div class="status info">🔄 连接语音机器人...</div>';
                
                const response = await fetch('/api/offer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        type: 'offer',
                        sdp: 'test'
                    })
                });
                
                if (response.ok) {
                    statusDiv.innerHTML = '<div class="status success">✅ 语音机器人API连接成功</div>';
                } else {
                    statusDiv.innerHTML = `<div class="status error">❌ 语音机器人API连接失败: ${response.status}</div>`;
                }
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ 语音机器人连接失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载时执行检查
        window.onload = function() {
            checkBrowserCompatibility();
        };
    </script>
</body>
</html>
