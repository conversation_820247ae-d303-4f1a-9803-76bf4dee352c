#!/usr/bin/env python3

"""
最终修复版中文语音对话机器人
完全遵循Pipecat开发指南，修复所有Pipeline错误
"""

import argparse
import asyncio
import os
import sys
from typing import Dict

import uvicorn
from dotenv import load_dotenv
from fastapi import BackgroundTasks, FastAPI
from fastapi.responses import RedirectResponse
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.services.openai.stt import OpenAISTTService
from pipecat.services.openai.tts import OpenAITTSService
from pipecat.transports.base_transport import TransportParams
from pipecat.transports.network.small_webrtc import SmallWebRTCTransport
from pipecat.transports.network.webrtc_connection import SmallWebRTCConnection
from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.transcriptions.language import Language

load_dotenv(override=True)


async def run_fixed_voice_bot(transport, args, handle_sigint: bool):
    """运行最终修复版中文语音对话机器人"""
    logger.info("🤖 启动最终修复版中文语音对话机器人")

    # 配置语音转文字服务 (STT) - 使用Groq的Whisper
    try:
        stt = OpenAISTTService(
            api_key=os.getenv("GROQ_API_KEY"),
            base_url="https://api.groq.com/openai/v1",
            model="whisper-large-v3",
            language=Language.ZH,
        )
        logger.info("✅ STT服务配置完成 (Groq Whisper)")
    except Exception as e:
        logger.error(f"❌ STT服务配置失败: {e}")
        # 使用OpenAI作为备选
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key:
            stt = OpenAISTTService(
                api_key=openai_key,
                model="whisper-1",
                language=Language.ZH,
            )
            logger.info("✅ STT服务配置完成 (OpenAI Whisper)")
        else:
            logger.error("❌ 没有可用的STT服务")
            return

    # 配置大语言模型服务 (LLM) - 使用DeepSeek
    try:
        llm = OpenAILLMService(
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            base_url="https://api.deepseek.com",
            model="deepseek-chat",
        )
        logger.info("✅ LLM服务配置完成 (DeepSeek)")
    except Exception as e:
        logger.error(f"❌ LLM服务配置失败: {e}")
        # 使用Groq作为备选
        try:
            llm = OpenAILLMService(
                api_key=os.getenv("GROQ_API_KEY"),
                base_url="https://api.groq.com/openai/v1",
                model="llama3-8b-8192",
            )
            logger.info("✅ LLM服务配置完成 (Groq Llama)")
        except Exception as e2:
            logger.error(f"❌ 备选LLM服务也失败: {e2}")
            return

    # 配置文字转语音服务 (TTS) - 使用OpenAI
    tts = None
    openai_key = os.getenv("OPENAI_API_KEY")
    if openai_key and openai_key.strip():
        try:
            tts = OpenAITTSService(
                api_key=openai_key,
                voice="alloy",
                model="tts-1",
            )
            logger.info("✅ TTS服务配置完成 (OpenAI)")
        except Exception as e:
            logger.warning(f"⚠️ TTS服务配置失败: {e}")
    
    if not tts:
        logger.warning("⚠️ 没有TTS服务，将只输出文本")

    # 配置LLM上下文管理器 - 中文对话设置
    context = OpenAILLMContext(
        messages=[
            {
                "role": "system",
                "content": """你是一个友好的中文AI语音助手。请遵循以下规则：
1. 用简洁、自然的中文回答用户的问题
2. 回答要简短明了，通常不超过30字
3. 语气要友好、亲切
4. 如果用户问候，要热情回应
5. 如果不理解用户的问题，礼貌地请求澄清
6. 保持对话的连贯性和上下文理解
7. 你的回答会被转换为语音，所以要适合口语表达"""
            }
        ]
    )
    context_aggregator = llm.create_context_aggregator(context)

    # 创建完整的语音处理管道 - 严格按照Pipecat指南
    if tts:
        # 有TTS的完整管道
        pipeline = Pipeline([
            transport.input(),              # WebRTC音频输入
            stt,                           # 语音转文字 (STT)
            context_aggregator.user(),     # 用户上下文聚合
            llm,                           # AI语言模型处理 (LLM)
            tts,                           # 文字转语音 (TTS)
            transport.output(),            # WebRTC音频输出
            context_aggregator.assistant(), # 助手上下文聚合
        ])
        logger.info("🔗 创建完整Pipeline: Input → STT → LLM → TTS → Output")
    else:
        # 没有TTS的简化管道
        pipeline = Pipeline([
            transport.input(),              # WebRTC音频输入
            stt,                           # 语音转文字 (STT)
            context_aggregator.user(),     # 用户上下文聚合
            llm,                           # AI语言模型处理 (LLM)
            transport.output(),            # WebRTC音频输出（文本）
            context_aggregator.assistant(), # 助手上下文聚合
        ])
        logger.info("🔗 创建简化Pipeline: Input → STT → LLM → Output")

    task = PipelineTask(pipeline)

    # 当客户端连接时的欢迎处理
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"✅ 客户端已连接: {client}")
        try:
            # 播放中文欢迎消息
            welcome_msg = "你好！我是您的中文语音助手。请开始说话，我会认真听取您的问题并回答。"
            if tts:
                await task.queue_frames([TTSSpeakFrame(welcome_msg)])
                logger.info("🎵 语音欢迎消息已发送")
            else:
                logger.info(f"📢 文本欢迎消息: {welcome_msg}")
        except Exception as e:
            logger.error(f"❌ 发送欢迎消息失败: {e}")

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client):
        logger.info(f"❌ 客户端已断开连接: {client}")

    @transport.event_handler("on_error")
    async def on_error(transport, error):
        logger.error(f"🚨 传输错误: {error}")

    runner = PipelineRunner(handle_sigint=handle_sigint)
    
    logger.info("🔄 开始运行最终修复版AI语音Pipeline...")
    await runner.run(task)


def create_fixed_app():
    """创建最终修复版FastAPI应用"""
    from pipecat_ai_small_webrtc_prebuilt.frontend import SmallWebRTCPrebuiltUI
    
    app = FastAPI(title="最终修复版中文语音对话机器人")
    
    # 存储WebRTC连接
    pcs_map: Dict[str, SmallWebRTCConnection] = {}
    
    # 挂载前端界面
    app.mount("/client", SmallWebRTCPrebuiltUI)
    
    @app.get("/", include_in_schema=False)
    async def root_redirect():
        return RedirectResponse(url="/client/")
    
    @app.post("/api/offer")
    async def offer(request: dict, background_tasks: BackgroundTasks):
        """处理WebRTC offer请求"""
        logger.info(f"📡 收到WebRTC offer请求: {request.get('pc_id', 'unknown')}")
        
        # 验证请求格式
        if "sdp" not in request or "type" not in request:
            logger.error(f"❌ 无效的WebRTC请求格式: {list(request.keys())}")
            return {"error": "Missing sdp or type in request", "status": "error"}
        
        pc_id = request.get("pc_id")
        
        try:
            if pc_id and pc_id in pcs_map:
                # 重用现有连接
                logger.info(f"🔄 重用现有连接: {pc_id}")
                pipecat_connection = pcs_map[pc_id]
                try:
                    await pipecat_connection.restart(
                        sdp=request["sdp"],
                        type=request["type"],
                        restart_pc=request.get("restart_pc", False),
                    )
                except Exception as restart_error:
                    logger.warning(f"⚠️ 连接重启失败，创建新连接: {restart_error}")
                    pipecat_connection = SmallWebRTCConnection()
                    await pipecat_connection.initialize(sdp=request["sdp"], type=request["type"])
            else:
                # 创建新连接
                logger.info(f"🆕 创建新的WebRTC连接")
                pipecat_connection = SmallWebRTCConnection()
                await pipecat_connection.initialize(sdp=request["sdp"], type=request["type"])
                
                @pipecat_connection.event_handler("closed")
                async def handle_disconnected(webrtc_connection):
                    logger.info(f"🔌 WebRTC连接已关闭: {webrtc_connection.pc_id}")
                    pcs_map.pop(webrtc_connection.pc_id, None)
                
                # 创建优化的传输参数
                params = TransportParams(
                    audio_out_enabled=True,
                    audio_in_enabled=True,
                    vad_enabled=True,
                    vad_analyzer=SileroVADAnalyzer(),
                    # 音频质量设置
                    audio_out_sample_rate=16000,
                    audio_out_channels=1,
                    # VAD参数优化 - 更敏感的语音检测
                    vad_confidence=0.6,      # 降低置信度要求
                    vad_start_secs=0.1,      # 更快开始检测
                    vad_stop_secs=0.5,       # 更快停止检测
                )
                
                # 创建传输并启动机器人
                transport = SmallWebRTCTransport(params=params, webrtc_connection=pipecat_connection)
                
                # 创建模拟的args对象
                class Args:
                    def __init__(self):
                        self.transport = "webrtc"
                        self.host = "0.0.0.0"
                        self.port = 7860
                
                args = Args()
                
                # 使用异步任务启动机器人
                asyncio.create_task(run_fixed_voice_bot(transport, args, False))
            
            answer = pipecat_connection.get_answer()
            pcs_map[answer["pc_id"]] = pipecat_connection
            
            logger.info(f"✅ WebRTC连接已建立: {answer['pc_id']}")
            return answer
            
        except Exception as e:
            logger.error(f"❌ WebRTC连接失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {"error": str(e), "status": "connection_failed"}
    
    return app


def main():
    """主函数"""
    logger.info("🚀 启动最终修复版中文语音对话机器人")
    
    # 检查必需的API密钥
    required_keys = ["GROQ_API_KEY", "DEEPSEEK_API_KEY"]
    missing_keys = [key for key in required_keys if not os.getenv(key)]
    
    if missing_keys:
        logger.warning(f"⚠️ 缺少API密钥: {missing_keys}")
        logger.info("💡 将尝试使用可用的服务")
    else:
        logger.info("✅ 必需API密钥检查通过")
    
    # 检查SSL证书
    ssl_certfile = "/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem"
    ssl_keyfile = "/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem"
    
    if not os.path.exists(ssl_certfile) or not os.path.exists(ssl_keyfile):
        logger.warning("⚠️ SSL证书文件不存在，使用HTTP模式")
        use_ssl = False
    else:
        logger.info("✅ SSL证书检查通过")
        use_ssl = True
    
    # 创建FastAPI应用
    app = create_fixed_app()
    
    # 启动服务器
    if use_ssl:
        logger.info("🔒 启动最终修复版HTTPS WebRTC服务器")
        logger.info("📱 访问地址: https://su.guiyunai.fun:7860")
        logger.info("🎯 功能: 完整的中文语音对话 (STT → LLM → TTS)")
        logger.info("🗣️ 支持: 中文语音识别、AI对话、语音合成")
        logger.info("⚡ 优化: 敏感的VAD设置，快速响应")
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=7860,
            ssl_keyfile=ssl_keyfile,
            ssl_certfile=ssl_certfile,
            log_level="info"
        )
    else:
        logger.info("🌐 启动最终修复版HTTP WebRTC服务器")
        logger.info("📱 访问地址: http://su.guiyunai.fun:7860")
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=7860,
            log_level="info"
        )


if __name__ == "__main__":
    main()
