#!/usr/bin/env python3

"""
智谱AI TTS服务测试脚本

测试智谱AI TTS服务的基本功能，验证语音合成是否正常工作。
"""

import asyncio
import os
import sys
from dotenv import load_dotenv
from loguru import logger

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from zhipu_tts_service import ZhipuTTSService
from pipecat.transcriptions.language import Language
from pipecat.frames.frames import StartFrame, EndFrame

load_dotenv(override=True)


async def test_zhipu_tts():
    """测试智谱AI TTS服务"""
    logger.info("🧪 开始测试智谱AI TTS服务")
    
    # 检查API密钥
    api_key = os.getenv("ZHIPU_API_KEY")
    if not api_key:
        logger.error("❌ 未找到ZHIPU_API_KEY环境变量")
        return False
    
    logger.info(f"✅ 找到智谱AI API密钥: {api_key[:20]}...")
    
    try:
        # 创建TTS服务实例
        tts = ZhipuTTSService(
            api_key=api_key,
            voice_id="zh-CN-XiaoxiaoNeural",
            params=ZhipuTTSService.InputParams(
                language=Language.ZH,
                speed=1.0,
                pitch=1.0,
                volume=1.0
            )
        )
        
        logger.info("✅ 智谱AI TTS服务实例创建成功")
        
        # 启动服务
        start_frame = StartFrame()
        await tts.start(start_frame)
        logger.info("✅ TTS服务启动成功")
        
        # 测试文本转语音
        test_texts = [
            "你好，我是智谱AI语音助手。",
            "今天天气很好。",
            "欢迎使用智谱AI TTS服务。"
        ]
        
        for i, text in enumerate(test_texts, 1):
            logger.info(f"🎵 测试文本 {i}: {text}")
            
            try:
                # 生成语音
                frames = []
                async for frame in tts.run_tts(text):
                    frames.append(frame)
                    logger.debug(f"收到帧: {type(frame).__name__}")
                
                logger.info(f"✅ 文本 {i} 处理完成，生成了 {len(frames)} 个帧")
                
                # 检查是否有音频帧
                audio_frames = [f for f in frames if hasattr(f, 'audio')]
                if audio_frames:
                    total_audio_size = sum(len(f.audio) for f in audio_frames)
                    logger.info(f"🎵 生成音频数据大小: {total_audio_size} 字节")
                else:
                    logger.warning("⚠️  未生成音频数据")
                    
            except Exception as e:
                logger.error(f"❌ 文本 {i} 处理失败: {e}")
                return False
        
        # 停止服务
        end_frame = EndFrame()
        await tts.stop(end_frame)
        logger.info("✅ TTS服务停止成功")
        
        logger.info("🎉 智谱AI TTS服务测试完成！")
        return True
        
    except Exception as e:
        logger.exception(f"❌ 测试过程中发生错误: {e}")
        return False


async def test_zhipu_api_connection():
    """测试智谱AI API连接"""
    logger.info("🔗 测试智谱AI API连接")
    
    api_key = os.getenv("ZHIPU_API_KEY")
    if not api_key:
        logger.error("❌ 未找到ZHIPU_API_KEY环境变量")
        return False
    
    try:
        from zhipuai import ZhipuAI
        
        client = ZhipuAI(api_key=api_key)
        logger.info("✅ 智谱AI客户端创建成功")
        
        # 测试一个简单的API调用
        # 注意：这里只是测试连接，不一定调用TTS API
        logger.info("🔍 测试API连接...")
        
        # 由于智谱AI的TTS API可能还在开发中，这里只测试客户端创建
        logger.info("✅ API连接测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ API连接测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    logger.info("🚀 开始智谱AI TTS服务测试")
    
    # 测试API连接
    api_ok = await test_zhipu_api_connection()
    if not api_ok:
        logger.error("❌ API连接测试失败，退出")
        return
    
    # 测试TTS服务
    tts_ok = await test_zhipu_tts()
    if tts_ok:
        logger.info("🎉 所有测试通过！")
    else:
        logger.error("❌ TTS服务测试失败")


if __name__ == "__main__":
    asyncio.run(main())
