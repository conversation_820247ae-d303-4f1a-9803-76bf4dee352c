# 智谱AI TTS集成指南

## 🎯 概述

本指南详细说明如何在pipecat语音机器人项目中使用智谱AI TTS服务。智谱AI提供高质量的中文语音合成服务，特别适合中文语音对话场景。

## ✅ 集成状态

- **状态**: 已完成集成并通过测试
- **API密钥**: 已配置 (`f0cb388b4b194279aef8897d4fa1bd95.PXSo4bqG2vNkbrF7`)
- **测试结果**: 全部通过 ✅

## 🚀 快速开始

### 1. 环境配置

确保`.env`文件中包含智谱AI API密钥：

```bash
# 智谱AI API密钥 (用于TTS)
ZHIPU_API_KEY=f0cb388b4b194279aef8897d4fa1bd95.PXSo4bqG2vNkbrF7

# 选择TTS提供商 (可选)
TTS_PROVIDER=zhipu
```

### 2. 安装依赖

```bash
# 激活虚拟环境
source env/bin/activate

# 安装智谱AI SDK
pip install zhipuai numpy
```

### 3. 启动语音机器人

```bash
python voice_bot_https.py
```

系统会自动检测并使用智谱AI TTS服务。

## 🔧 技术实现

### 服务类结构

```python
class ZhipuTTSService(TTSService):
    """智谱AI文本转语音服务实现"""
    
    def __init__(self, api_key, voice_id, params=None):
        # 初始化智谱AI TTS服务
        
    async def run_tts(self, text: str):
        # 执行文本转语音合成
```

### 配置参数

```python
# 创建智谱AI TTS服务
tts = ZhipuTTSService(
    api_key=os.getenv("ZHIPU_API_KEY"),
    voice_id="zh-CN-XiaoxiaoNeural",  # 中文女声
    params=ZhipuTTSService.InputParams(
        language=Language.ZH,  # 中文
        speed=1.0,            # 语速
        pitch=1.0,            # 音调
        volume=1.0            # 音量
    )
)
```

## 🧪 测试验证

### 运行测试脚本

```bash
python test_zhipu_tts.py
```

### 测试结果示例

```
🚀 开始智谱AI TTS服务测试
🔗 测试智谱AI API连接
✅ 智谱AI客户端创建成功
✅ API连接测试完成
🧪 开始测试智谱AI TTS服务
✅ 找到智谱AI API密钥: f0cb388b4b194279aef8...
✅ 智谱AI TTS服务实例创建成功
✅ TTS服务启动成功
🎵 测试文本 1: 你好，我是智谱AI语音助手。
✅ 文本 1 处理完成，生成了 3 个帧
🎵 生成音频数据大小: 67200 字节
🎉 所有测试通过！
```

## 📁 文件结构

```
pipecat-voice-ai/
├── zhipu_tts_service.py      # 智谱AI TTS服务实现
├── test_zhipu_tts.py         # TTS服务测试脚本
├── voice_bot_https.py        # 主要语音机器人文件 (已更新)
├── .env                      # 环境配置 (包含API密钥)
├── requirements.txt          # 依赖列表 (已添加zhipuai)
└── ZHIPU_TTS_GUIDE.md       # 本指南文件
```

## ⚙️ 配置选项

### TTS提供商选择

在`voice_bot_https.py`中，系统会根据环境变量自动选择TTS提供商：

```python
tts_provider = os.getenv("TTS_PROVIDER", "zhipu").lower()

if tts_provider == "zhipu" and os.getenv("ZHIPU_API_KEY"):
    # 使用智谱AI TTS
    tts = ZhipuTTSService(...)
else:
    # 备选方案：使用Groq TTS
    tts = GroqTTSService(...)
```

### 语音参数调节

```python
params = ZhipuTTSService.InputParams(
    language=Language.ZH,    # 语言: ZH(中文), EN(英文)
    speed=1.0,              # 语速: 0.5-2.0
    pitch=1.0,              # 音调: 0.5-2.0  
    volume=1.0              # 音量: 0.1-2.0
)
```

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   ```
   ❌ 未找到ZHIPU_API_KEY环境变量
   ```
   **解决方案**: 检查`.env`文件中的`ZHIPU_API_KEY`配置

2. **依赖缺失**
   ```
   ModuleNotFoundError: No module named 'zhipuai'
   ```
   **解决方案**: 运行 `pip install zhipuai numpy`

3. **音频生成失败**
   ```
   ❌ TTS error: ...
   ```
   **解决方案**: 检查网络连接和API密钥有效性

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🎵 音频质量

- **采样率**: 24kHz (高质量)
- **声道**: 单声道
- **格式**: PCM 16-bit
- **延迟**: 低延迟流式合成
- **语言支持**: 中文优化

## 🔄 后续优化

1. **实时流式合成**: 进一步降低延迟
2. **多语音选择**: 支持更多音色
3. **情感控制**: 添加情感参数
4. **SSML支持**: 支持语音标记语言
5. **缓存机制**: 优化重复文本的处理

## 📞 技术支持

如有问题，请查看：
1. 测试脚本输出: `python test_zhipu_tts.py`
2. 日志文件: 检查详细错误信息
3. API文档: 智谱AI官方文档

---

**集成完成时间**: 2025-08-01  
**测试状态**: ✅ 全部通过  
**推荐使用**: 🌟 适合中文语音合成场景
