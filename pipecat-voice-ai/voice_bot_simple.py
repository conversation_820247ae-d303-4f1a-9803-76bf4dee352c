#!/usr/bin/env python3

"""
简化版中文语音对话机器人
使用本地TTS和简化的LLM响应进行测试
"""

import argparse
import asyncio
import os
import sys
from typing import Dict

import uvicorn
from dotenv import load_dotenv
from fastapi import BackgroundTasks, FastAPI
from fastapi.responses import RedirectResponse
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.services.openai.stt import OpenAISTTService
from pipecat.services.openai.tts import OpenAITTSService
from pipecat.transports.base_transport import TransportParams
from pipecat.transports.network.small_webrtc import SmallWebRTCTransport
from pipecat.transports.network.webrtc_connection import SmallWebRTCConnection
from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.transcriptions.language import Language

load_dotenv(override=True)


class SimpleLLMService:
    """简化的LLM服务，用于测试"""
    
    def __init__(self):
        self.responses = [
            "你好！我是您的语音助手，很高兴为您服务。",
            "我听到了您的话，正在为您处理。",
            "感谢您的提问，我会尽力帮助您。",
            "这是一个很好的问题，让我想想。",
            "我理解您的意思，请稍等片刻。"
        ]
        self.response_index = 0
    
    def create_context_aggregator(self, context):
        """创建上下文聚合器"""
        return MockContextAggregator()
    
    async def process_frame(self, frame):
        """处理输入帧并返回响应"""
        # 简单的轮询响应
        response = self.responses[self.response_index % len(self.responses)]
        self.response_index += 1
        
        # 返回TTS帧
        return TTSSpeakFrame(response)


class MockContextAggregator:
    """模拟上下文聚合器"""
    
    def user(self):
        return self
    
    def assistant(self):
        return self
    
    async def process_frame(self, frame):
        return frame


async def run_simple_voice_bot(transport, args, handle_sigint: bool):
    """运行简化版中文语音对话机器人"""
    logger.info("🤖 启动简化版中文语音对话机器人")

    # 配置语音转文字服务 (STT) - 使用OpenAI Whisper (如果有API key)
    openai_key = os.getenv("OPENAI_API_KEY")
    if openai_key and openai_key.strip():
        logger.info("🎤 使用OpenAI STT服务")
        stt = OpenAISTTService(
            api_key=openai_key,
            model="whisper-1",
            language=Language.ZH,
        )
    else:
        logger.info("🎤 使用Groq STT服务")
        stt = OpenAISTTService(
            api_key=os.getenv("GROQ_API_KEY", "dummy-key"),
            base_url="https://api.groq.com/openai/v1",
            model="whisper-large-v3",
            language=Language.ZH,
        )

    # 使用简化的LLM服务
    logger.info("🧠 使用简化LLM服务")
    llm = SimpleLLMService()

    # 配置文字转语音服务 (TTS)
    if openai_key and openai_key.strip():
        logger.info("🔊 使用OpenAI TTS服务")
        tts = OpenAITTSService(
            api_key=openai_key,
            voice="alloy",
            model="tts-1",
        )
    else:
        logger.info("🔊 使用简化TTS服务 (回声测试)")
        # 如果没有TTS服务，使用简单的回声
        tts = None

    # 配置LLM上下文管理器
    context = OpenAILLMContext(
        messages=[
            {
                "role": "system",
                "content": "你是一个友好的中文AI语音助手。请用简洁、自然的中文回答。"
            }
        ]
    )
    context_aggregator = llm.create_context_aggregator(context)

    # 创建简化的语音处理管道
    pipeline_components = [
        transport.input(),           # WebRTC音频输入
        stt,                        # 语音转文字
        context_aggregator.user(),  # 用户上下文聚合
        llm,                        # 简化LLM处理
    ]
    
    if tts:
        pipeline_components.extend([
            tts,                        # 文字转语音
            transport.output(),         # WebRTC音频输出
            context_aggregator.assistant(),  # 助手上下文聚合
        ])
    else:
        # 没有TTS时，直接输出文本
        pipeline_components.extend([
            transport.output(),
            context_aggregator.assistant(),
        ])

    pipeline = Pipeline(pipeline_components)
    task = PipelineTask(pipeline)

    # 当客户端连接时的欢迎处理
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"✅ 客户端已连接: {client}")
        try:
            # 播放中文欢迎消息
            welcome_msg = "你好！我是您的简化版语音助手。请开始说话，我会尽力回应您。"
            if tts:
                await task.queue_frames([TTSSpeakFrame(welcome_msg)])
            else:
                logger.info(f"📢 欢迎消息: {welcome_msg}")
            logger.info("🎵 欢迎消息已发送")
        except Exception as e:
            logger.error(f"❌ 发送欢迎消息失败: {e}")

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client):
        logger.info(f"❌ 客户端已断开连接: {client}")

    @transport.event_handler("on_error")
    async def on_error(transport, error):
        logger.error(f"🚨 传输错误: {error}")

    runner = PipelineRunner(handle_sigint=handle_sigint)
    
    logger.info("🔄 开始运行简化版语音Pipeline...")
    await runner.run(task)


def create_simple_app():
    """创建简化版FastAPI应用"""
    from pipecat_ai_small_webrtc_prebuilt.frontend import SmallWebRTCPrebuiltUI
    
    app = FastAPI(title="简化版中文语音对话机器人")
    
    # 存储WebRTC连接
    pcs_map: Dict[str, SmallWebRTCConnection] = {}
    
    # 挂载前端界面
    app.mount("/client", SmallWebRTCPrebuiltUI)
    
    @app.get("/", include_in_schema=False)
    async def root_redirect():
        return RedirectResponse(url="/client/")
    
    @app.post("/api/offer")
    async def offer(request: dict, background_tasks: BackgroundTasks):
        """处理WebRTC offer请求"""
        logger.info(f"📡 收到WebRTC offer请求: {request.get('pc_id', 'unknown')}")

        # 验证请求格式
        if "sdp" not in request or "type" not in request:
            logger.error(f"❌ 无效的WebRTC请求格式: {list(request.keys())}")
            return {"error": "Missing sdp or type in request"}

        pc_id = request.get("pc_id")

        try:
            if pc_id and pc_id in pcs_map:
                # 重用现有连接
                logger.info(f"🔄 重用现有连接: {pc_id}")
                pipecat_connection = pcs_map[pc_id]
                await pipecat_connection.restart(
                    sdp=request["sdp"],
                    type=request["type"],
                    restart_pc=request.get("restart_pc", False),
                )
            else:
                # 创建新连接
                logger.info(f"🆕 创建新的WebRTC连接")
                pipecat_connection = SmallWebRTCConnection()
                await pipecat_connection.initialize(sdp=request["sdp"], type=request["type"])
                
                @pipecat_connection.event_handler("closed")
                async def handle_disconnected(webrtc_connection):
                    logger.info(f"🔌 WebRTC连接已关闭: {webrtc_connection.pc_id}")
                    pcs_map.pop(webrtc_connection.pc_id, None)
                
                # 创建传输参数
                params = TransportParams(
                    audio_out_enabled=True,
                    audio_in_enabled=True,
                    vad_enabled=True,
                    vad_analyzer=SileroVADAnalyzer(),
                    audio_out_sample_rate=16000,
                    audio_out_channels=1,
                    vad_confidence=0.6,
                    vad_start_secs=0.1,
                    vad_stop_secs=0.5,
                )
                
                # 创建传输并启动简化AI机器人
                transport = SmallWebRTCTransport(params=params, webrtc_connection=pipecat_connection)
                
                # 创建模拟的args对象
                class Args:
                    def __init__(self):
                        self.transport = "webrtc"
                        self.host = "0.0.0.0"
                        self.port = 7860
                
                args = Args()
                
                # 使用异步任务启动简化AI机器人
                asyncio.create_task(run_simple_voice_bot(transport, args, False))
            
            answer = pipecat_connection.get_answer()
            pcs_map[answer["pc_id"]] = pipecat_connection
            
            logger.info(f"✅ WebRTC连接已建立: {answer['pc_id']}")
            return answer
            
        except Exception as e:
            logger.error(f"❌ WebRTC连接失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            raise
    
    return app


def main():
    """主函数"""
    logger.info("🚀 启动简化版中文语音对话机器人")
    
    # 检查SSL证书
    ssl_certfile = "/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem"
    ssl_keyfile = "/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem"
    
    if not os.path.exists(ssl_certfile) or not os.path.exists(ssl_keyfile):
        logger.warning("⚠️ SSL证书文件不存在，使用HTTP模式")
        use_ssl = False
    else:
        logger.info("✅ SSL证书检查通过")
        use_ssl = True
    
    # 创建FastAPI应用
    app = create_simple_app()
    
    # 启动服务器
    if use_ssl:
        logger.info("🔒 启动简化版HTTPS WebRTC服务器")
        logger.info("📱 访问地址: https://su.guiyunai.fun:7860")
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=7860,
            ssl_keyfile=ssl_keyfile,
            ssl_certfile=ssl_certfile,
            log_level="info"
        )
    else:
        logger.info("🌐 启动简化版HTTP WebRTC服务器")
        logger.info("📱 访问地址: http://su.guiyunai.fun:7860")
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=7860,
            log_level="info"
        )


if __name__ == "__main__":
    main()
