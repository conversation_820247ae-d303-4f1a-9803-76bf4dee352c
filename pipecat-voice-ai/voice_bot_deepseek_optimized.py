#!/usr/bin/env python3

"""
DeepSeek优化版中文语音对话机器人
专门针对DeepSeek API优化，支持多种TTS方案
"""

import argparse
import asyncio
import os
import sys
from typing import Dict

import uvicorn
from dotenv import load_dotenv
from fastapi import BackgroundTasks, FastAPI
from fastapi.responses import RedirectResponse
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.services.openai.stt import OpenAISTTService
from pipecat.services.openai.tts import OpenAITTSService
from pipecat.transports.base_transport import TransportParams
from pipecat.transports.network.small_webrtc import SmallWebRTCTransport
from pipecat.transports.network.webrtc_connection import SmallWebRTCConnection
from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.transcriptions.language import Language

load_dotenv(override=True)


async def run_deepseek_voice_bot(transport, args, handle_sigint: bool):
    """运行DeepSeek优化版中文语音对话机器人"""
    logger.info("🤖 启动DeepSeek优化版中文语音对话机器人")

    # 配置语音转文字服务 (STT) - 优先使用Groq，备选OpenAI
    stt = None
    groq_key = os.getenv("GROQ_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    
    if groq_key and groq_key.strip():
        try:
            stt = OpenAISTTService(
                api_key=groq_key,
                base_url="https://api.groq.com/openai/v1",
                model="whisper-large-v3",
                language=Language.ZH,
            )
            logger.info("✅ STT服务配置完成 (Groq Whisper)")
        except Exception as e:
            logger.warning(f"⚠️ Groq STT配置失败: {e}")
    
    if not stt and openai_key and openai_key.strip():
        try:
            stt = OpenAISTTService(
                api_key=openai_key,
                model="whisper-1",
                language=Language.ZH,
            )
            logger.info("✅ STT服务配置完成 (OpenAI Whisper)")
        except Exception as e:
            logger.warning(f"⚠️ OpenAI STT配置失败: {e}")
    
    if not stt:
        logger.error("❌ 没有可用的STT服务")
        return

    # 配置DeepSeek LLM服务
    deepseek_key = os.getenv("DEEPSEEK_API_KEY")
    if not deepseek_key:
        logger.error("❌ 缺少DEEPSEEK_API_KEY")
        return
    
    try:
        llm = OpenAILLMService(
            api_key=deepseek_key,
            base_url="https://api.deepseek.com",
            model="deepseek-chat",
        )
        logger.info("✅ DeepSeek LLM服务配置完成")
    except Exception as e:
        logger.error(f"❌ DeepSeek LLM配置失败: {e}")
        return

    # 配置TTS服务 - 多种选项
    tts = None
    
    # 选项1: OpenAI TTS (推荐)
    if openai_key and openai_key.strip():
        try:
            tts = OpenAITTSService(
                api_key=openai_key,
                voice="alloy",
                model="tts-1",
            )
            logger.info("✅ TTS服务配置完成 (OpenAI)")
        except Exception as e:
            logger.warning(f"⚠️ OpenAI TTS配置失败: {e}")
    
    # 选项2: 如果没有TTS，使用文本输出
    if not tts:
        logger.warning("⚠️ 没有TTS服务，将使用文本输出模式")

    # 配置DeepSeek专用的LLM上下文
    context = OpenAILLMContext(
        messages=[
            {
                "role": "system",
                "content": """你是一个专业的中文AI语音助手，基于DeepSeek模型。请遵循以下规则：

🎯 回答风格：
1. 用自然、流畅的中文回答
2. 回答要简洁明了，通常20-40字
3. 语气友好、专业、有帮助
4. 适合语音播报的口语化表达

🗣️ 语音优化：
1. 避免使用复杂的标点符号
2. 数字用中文表达（如"三个"而不是"3个"）
3. 避免使用括号、引号等特殊符号
4. 句子结构简单清晰

💬 对话策略：
1. 如果用户问候，热情回应
2. 如果问题不清楚，礼貌询问
3. 保持上下文连贯性
4. 提供实用、准确的信息

🚀 DeepSeek特色：
1. 展现逻辑思维能力
2. 提供深度分析
3. 保持客观理性
4. 注重实用性"""
            }
        ]
    )
    context_aggregator = llm.create_context_aggregator(context)

    # 创建优化的Pipeline
    if tts:
        # 完整的语音对话Pipeline
        pipeline = Pipeline([
            transport.input(),              # WebRTC音频输入
            stt,                           # 语音转文字
            context_aggregator.user(),     # 用户上下文聚合
            llm,                           # DeepSeek LLM处理
            tts,                           # 文字转语音
            transport.output(),            # WebRTC音频输出
            context_aggregator.assistant(), # 助手上下文聚合
        ])
        logger.info("🔗 创建完整Pipeline: Input → STT → DeepSeek → TTS → Output")
    else:
        # 文本输出Pipeline
        pipeline = Pipeline([
            transport.input(),              # WebRTC音频输入
            stt,                           # 语音转文字
            context_aggregator.user(),     # 用户上下文聚合
            llm,                           # DeepSeek LLM处理
            transport.output(),            # WebRTC输出（文本）
            context_aggregator.assistant(), # 助手上下文聚合
        ])
        logger.info("🔗 创建文本Pipeline: Input → STT → DeepSeek → Output")

    task = PipelineTask(pipeline)

    # 客户端连接处理
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"✅ 客户端已连接: {client}")
        try:
            welcome_msg = "你好！我是基于DeepSeek的中文语音助手。我可以进行深度思考和分析，请开始和我对话吧！"
            if tts:
                await task.queue_frames([TTSSpeakFrame(welcome_msg)])
                logger.info("🎵 DeepSeek语音欢迎消息已发送")
            else:
                logger.info(f"📢 DeepSeek文本欢迎消息: {welcome_msg}")
        except Exception as e:
            logger.error(f"❌ 发送欢迎消息失败: {e}")

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client):
        logger.info(f"❌ 客户端已断开连接: {client}")

    @transport.event_handler("on_error")
    async def on_error(transport, error):
        logger.error(f"🚨 传输错误: {error}")

    runner = PipelineRunner(handle_sigint=handle_sigint)
    
    logger.info("🔄 开始运行DeepSeek优化版AI语音Pipeline...")
    await runner.run(task)


def create_deepseek_app():
    """创建DeepSeek优化版FastAPI应用"""
    from pipecat_ai_small_webrtc_prebuilt.frontend import SmallWebRTCPrebuiltUI
    
    app = FastAPI(title="DeepSeek优化版中文语音对话机器人")
    
    # 存储WebRTC连接
    pcs_map: Dict[str, SmallWebRTCConnection] = {}
    
    # 挂载前端界面
    app.mount("/client", SmallWebRTCPrebuiltUI)
    
    @app.get("/", include_in_schema=False)
    async def root_redirect():
        return RedirectResponse(url="/client/")
    
    @app.post("/api/offer")
    async def offer(request: dict, background_tasks: BackgroundTasks):
        """处理WebRTC offer请求"""
        logger.info(f"📡 收到WebRTC offer请求: {request.get('pc_id', 'unknown')}")
        
        # 验证请求格式
        if "sdp" not in request or "type" not in request:
            logger.error(f"❌ 无效的WebRTC请求格式: {list(request.keys())}")
            return {"error": "Missing sdp or type in request", "status": "error"}
        
        pc_id = request.get("pc_id")
        
        try:
            if pc_id and pc_id in pcs_map:
                # 重用现有连接
                logger.info(f"🔄 重用现有连接: {pc_id}")
                pipecat_connection = pcs_map[pc_id]
                try:
                    await pipecat_connection.restart(
                        sdp=request["sdp"],
                        type=request["type"],
                        restart_pc=request.get("restart_pc", False),
                    )
                except Exception as restart_error:
                    logger.warning(f"⚠️ 连接重启失败，创建新连接: {restart_error}")
                    pipecat_connection = SmallWebRTCConnection()
                    await pipecat_connection.initialize(sdp=request["sdp"], type=request["type"])
            else:
                # 创建新连接
                logger.info(f"🆕 创建新的WebRTC连接")
                pipecat_connection = SmallWebRTCConnection()
                await pipecat_connection.initialize(sdp=request["sdp"], type=request["type"])
                
                @pipecat_connection.event_handler("closed")
                async def handle_disconnected(webrtc_connection):
                    logger.info(f"🔌 WebRTC连接已关闭: {webrtc_connection.pc_id}")
                    pcs_map.pop(webrtc_connection.pc_id, None)
                
                # DeepSeek优化的传输参数
                params = TransportParams(
                    audio_out_enabled=True,
                    audio_in_enabled=True,
                    vad_enabled=True,
                    vad_analyzer=SileroVADAnalyzer(),
                    # 音频质量设置
                    audio_out_sample_rate=16000,
                    audio_out_channels=1,
                    # 针对DeepSeek优化的VAD参数
                    vad_confidence=0.65,     # 适中的置信度
                    vad_start_secs=0.15,     # 快速开始
                    vad_stop_secs=0.6,       # 给DeepSeek思考时间
                )
                
                # 创建传输并启动DeepSeek机器人
                transport = SmallWebRTCTransport(params=params, webrtc_connection=pipecat_connection)
                
                # 创建args对象
                class Args:
                    def __init__(self):
                        self.transport = "webrtc"
                        self.host = "0.0.0.0"
                        self.port = 7860
                
                args = Args()
                
                # 启动DeepSeek机器人
                asyncio.create_task(run_deepseek_voice_bot(transport, args, False))
            
            answer = pipecat_connection.get_answer()
            pcs_map[answer["pc_id"]] = pipecat_connection
            
            logger.info(f"✅ WebRTC连接已建立: {answer['pc_id']}")
            return answer
            
        except Exception as e:
            logger.error(f"❌ WebRTC连接失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {"error": str(e), "status": "connection_failed"}
    
    return app


def main():
    """主函数"""
    logger.info("🚀 启动DeepSeek优化版中文语音对话机器人")
    
    # 检查DeepSeek API密钥
    deepseek_key = os.getenv("DEEPSEEK_API_KEY")
    if not deepseek_key:
        logger.error("❌ 缺少DEEPSEEK_API_KEY")
        logger.info("💡 请在.env文件中设置: DEEPSEEK_API_KEY=your_key_here")
        sys.exit(1)
    else:
        logger.info("✅ DeepSeek API密钥检查通过")
    
    # 检查其他API密钥
    groq_key = os.getenv("GROQ_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    
    if not groq_key and not openai_key:
        logger.warning("⚠️ 缺少STT服务API密钥 (GROQ_API_KEY 或 OPENAI_API_KEY)")
        logger.info("💡 建议设置至少一个STT服务密钥")
    
    if not openai_key:
        logger.warning("⚠️ 缺少OPENAI_API_KEY，将无法使用TTS语音输出")
        logger.info("💡 如需语音输出，请设置OPENAI_API_KEY")
    
    # 检查SSL证书
    ssl_certfile = "/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem"
    ssl_keyfile = "/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem"
    
    if not os.path.exists(ssl_certfile) or not os.path.exists(ssl_keyfile):
        logger.warning("⚠️ SSL证书文件不存在，使用HTTP模式")
        use_ssl = False
    else:
        logger.info("✅ SSL证书检查通过")
        use_ssl = True
    
    # 创建FastAPI应用
    app = create_deepseek_app()
    
    # 启动服务器
    if use_ssl:
        logger.info("🔒 启动DeepSeek优化版HTTPS WebRTC服务器")
        logger.info("📱 访问地址: https://su.guiyunai.fun:7860")
        logger.info("🎯 功能: DeepSeek中文语音对话 (STT → DeepSeek → TTS)")
        logger.info("🧠 特色: 深度思考、逻辑分析、专业回答")
        logger.info("⚡ 优化: 针对DeepSeek的VAD和上下文设置")
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=7860,
            ssl_keyfile=ssl_keyfile,
            ssl_certfile=ssl_certfile,
            log_level="info"
        )
    else:
        logger.info("🌐 启动DeepSeek优化版HTTP WebRTC服务器")
        logger.info("📱 访问地址: http://su.guiyunai.fun:7860")
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=7860,
            log_level="info"
        )


if __name__ == "__main__":
    main()
