#!/usr/bin/env python3
"""
测试DeepSeek API连接和基本功能
"""

import os
import asyncio
import json
from dotenv import load_dotenv
import httpx

load_dotenv()

async def test_deepseek_api():
    """测试DeepSeek API连接"""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ 未找到DEEPSEEK_API_KEY环境变量")
        return False
    
    print(f"🔑 使用API密钥: {api_key[:20]}...")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # 构建测试请求
            request_data = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一位专业的风水大师，请用中文回答问题。"
                    },
                    {
                        "role": "user", 
                        "content": "你好，请简单介绍一下什么是风水？"
                    }
                ],
                "stream": False,
                "temperature": 0.7,
                "max_tokens": 500
            }
            
            print("📡 发送测试请求到DeepSeek API...")
            
            response = await client.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                },
                json=request_data,
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if "choices" in result and len(result["choices"]) > 0:
                    content = result["choices"][0]["message"]["content"]
                    print("✅ DeepSeek API测试成功！")
                    print(f"🤖 AI回复: {content}")
                    
                    # 显示使用统计
                    if "usage" in result:
                        usage = result["usage"]
                        print(f"📈 Token使用: {usage.get('total_tokens', 0)} tokens")
                    
                    return True
                else:
                    print("❌ API响应格式异常")
                    print(f"响应内容: {result}")
                    return False
            else:
                error_text = response.text
                print(f"❌ API请求失败: {response.status_code}")
                print(f"错误信息: {error_text}")
                return False
                
    except Exception as e:
        print(f"❌ 连接DeepSeek API时出错: {e}")
        return False

async def test_streaming_api():
    """测试DeepSeek流式API"""
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ 未找到DEEPSEEK_API_KEY环境变量")
        return False
    
    print("\n🌊 测试流式API...")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            request_data = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一位专业的风水大师，请用中文回答问题。"
                    },
                    {
                        "role": "user", 
                        "content": "请简单说明客厅风水的三个要点。"
                    }
                ],
                "stream": True,
                "temperature": 0.7,
                "max_tokens": 300
            }
            
            print("📡 发送流式请求...")
            
            async with client.stream(
                "POST",
                "https://api.deepseek.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                },
                json=request_data,
            ) as response:
                
                if response.status_code != 200:
                    error_text = await response.aread()
                    print(f"❌ 流式API请求失败: {response.status_code}")
                    print(f"错误信息: {error_text}")
                    return False
                
                print("🤖 AI流式回复:")
                full_response = ""
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # 移除"data: "前缀
                        
                        if data.strip() == "[DONE]":
                            break
                        
                        try:
                            chunk = json.loads(data)
                            
                            if "choices" in chunk and len(chunk["choices"]) > 0:
                                choice = chunk["choices"][0]
                                
                                if "delta" in choice and "content" in choice["delta"]:
                                    content = choice["delta"]["content"]
                                    if content:
                                        print(content, end="", flush=True)
                                        full_response += content
                        
                        except json.JSONDecodeError:
                            continue
                
                print("\n✅ 流式API测试成功！")
                print(f"📝 完整回复长度: {len(full_response)} 字符")
                return True
                
    except Exception as e:
        print(f"❌ 流式API测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 开始测试DeepSeek API集成")
    print("=" * 50)
    
    # 测试基本API
    basic_success = await test_deepseek_api()
    
    if basic_success:
        # 测试流式API
        streaming_success = await test_streaming_api()
        
        if streaming_success:
            print("\n🎉 所有测试通过！DeepSeek API集成正常")
            print("\n📋 下一步:")
            print("1. 申请Deepgram API密钥 (语音识别)")
            print("2. 申请Cartesia API密钥 (语音合成)")
            print("3. 申请LiveKit Cloud账号 (实时通信)")
            print("4. 运行完整的LiveKit Agent")
        else:
            print("\n⚠️ 流式API测试失败，但基本功能正常")
    else:
        print("\n❌ 基本API测试失败，请检查API密钥和网络连接")
        print("\n🔧 故障排除:")
        print("1. 检查.env文件中的DEEPSEEK_API_KEY是否正确")
        print("2. 确认网络可以访问api.deepseek.com")
        print("3. 验证API密钥是否有效且有余额")

if __name__ == "__main__":
    asyncio.run(main())
