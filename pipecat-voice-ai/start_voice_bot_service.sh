#!/bin/bash

# 智谱AI语音机器人服务启动脚本
# 用于启动和管理语音机器人后端服务

set -e

# 配置
PROJECT_DIR="/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai"
VENV_DIR="$PROJECT_DIR/env"
PYTHON_SCRIPT="voice_bot_https.py"
PID_FILE="$PROJECT_DIR/voice_bot.pid"
LOG_FILE="$PROJECT_DIR/voice_bot.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查项目目录
check_project() {
    if [[ ! -d "$PROJECT_DIR" ]]; then
        error "项目目录不存在: $PROJECT_DIR"
        exit 1
    fi
    
    if [[ ! -f "$PROJECT_DIR/$PYTHON_SCRIPT" ]]; then
        error "Python脚本不存在: $PROJECT_DIR/$PYTHON_SCRIPT"
        exit 1
    fi
    
    if [[ ! -d "$VENV_DIR" ]]; then
        error "虚拟环境不存在: $VENV_DIR"
        exit 1
    fi
}

# 检查环境变量
check_env() {
    cd "$PROJECT_DIR"
    source "$VENV_DIR/bin/activate"
    
    if ! python -c "
import os
from dotenv import load_dotenv
load_dotenv()
groq_key = os.getenv('GROQ_API_KEY')
zhipu_key = os.getenv('ZHIPU_API_KEY')
if not groq_key:
    print('ERROR: GROQ_API_KEY not found')
    exit(1)
if not zhipu_key:
    print('ERROR: ZHIPU_API_KEY not found')
    exit(1)
print('Environment variables OK')
"; then
        error "环境变量检查失败"
        exit 1
    fi
}

# 检查端口占用
check_port() {
    local port=8081
    if ss -tlnp | grep -q ":$port "; then
        local pid=$(ss -tlnp | grep ":$port " | grep -o 'pid=[0-9]*' | cut -d'=' -f2)
        if [[ -n "$pid" ]]; then
            warning "端口 $port 已被进程 $pid 占用"
            return 1
        fi
    fi
    return 0
}

# 启动服务
start_service() {
    log "启动智谱AI语音机器人服务..."
    
    # 检查环境
    check_project
    check_env
    
    # 检查是否已经运行
    if [[ -f "$PID_FILE" ]]; then
        local old_pid=$(cat "$PID_FILE")
        if kill -0 "$old_pid" 2>/dev/null; then
            warning "服务已经在运行 (PID: $old_pid)"
            return 0
        else
            log "清理旧的PID文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 检查端口
    if ! check_port; then
        error "端口8081被占用，请先停止占用进程"
        exit 1
    fi
    
    # 切换到项目目录
    cd "$PROJECT_DIR"
    
    # 激活虚拟环境并启动服务
    source "$VENV_DIR/bin/activate"
    
    # 后台启动服务
    nohup python "$PYTHON_SCRIPT" > "$LOG_FILE" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo "$pid" > "$PID_FILE"
    
    # 等待服务启动
    sleep 3
    
    # 检查服务是否成功启动
    if kill -0 "$pid" 2>/dev/null; then
        success "服务启动成功 (PID: $pid)"
        success "日志文件: $LOG_FILE"
        success "访问地址: https://su.guiyunai.fun/"
    else
        error "服务启动失败"
        if [[ -f "$LOG_FILE" ]]; then
            error "错误日志:"
            tail -10 "$LOG_FILE"
        fi
        exit 1
    fi
}

# 停止服务
stop_service() {
    log "停止智谱AI语音机器人服务..."
    
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            sleep 2
            
            # 强制杀死如果还在运行
            if kill -0 "$pid" 2>/dev/null; then
                kill -9 "$pid"
                sleep 1
            fi
            
            rm -f "$PID_FILE"
            success "服务已停止"
        else
            warning "服务未运行"
            rm -f "$PID_FILE"
        fi
    else
        warning "PID文件不存在，服务可能未运行"
    fi
}

# 重启服务
restart_service() {
    log "重启智谱AI语音机器人服务..."
    stop_service
    sleep 2
    start_service
}

# 查看服务状态
status_service() {
    log "检查智谱AI语音机器人服务状态..."
    
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            success "服务正在运行 (PID: $pid)"
            
            # 检查端口
            if ss -tlnp | grep -q ":8081 "; then
                success "端口8081正在监听"
            else
                warning "端口8081未监听"
            fi
            
            # 检查日志
            if [[ -f "$LOG_FILE" ]]; then
                log "最近日志:"
                tail -5 "$LOG_FILE"
            fi
        else
            error "服务未运行 (PID文件存在但进程不存在)"
            rm -f "$PID_FILE"
        fi
    else
        warning "服务未运行 (PID文件不存在)"
    fi
}

# 查看日志
logs_service() {
    if [[ -f "$LOG_FILE" ]]; then
        log "显示服务日志 (按Ctrl+C退出):"
        tail -f "$LOG_FILE"
    else
        warning "日志文件不存在: $LOG_FILE"
    fi
}

# 主函数
main() {
    check_root
    
    case "${1:-}" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            status_service
            ;;
        logs)
            logs_service
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status|logs}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动语音机器人服务"
            echo "  stop    - 停止语音机器人服务"
            echo "  restart - 重启语音机器人服务"
            echo "  status  - 查看服务状态"
            echo "  logs    - 查看服务日志"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
