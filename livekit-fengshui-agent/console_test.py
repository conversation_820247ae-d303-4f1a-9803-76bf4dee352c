#!/usr/bin/env python3
"""
控制台模式测试 - 不需要LiveKit服务器，直接测试DeepSeek对话
"""

import os
import asyncio
import json
from dotenv import load_dotenv
import httpx

load_dotenv()

class FengshuiConsoleBot:
    """风水控制台机器人"""
    
    def __init__(self):
        self.api_key = os.getenv("DEEPSEEK_API_KEY")
        if not self.api_key:
            raise ValueError("请设置DEEPSEEK_API_KEY环境变量")
        
        self.conversation_history = [
            {
                "role": "system",
                "content": """你是张大师，一位拥有30年经验的专业风水大师。

🎯 **你的专长**：住宅风水,商业风水,墓地风水

🗣️ **对话风格**：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释复杂的风水概念
- 结合传统风水理论和现代生活实际
- 给出具体可行的建议

📋 **服务内容**：
- 分析房屋布局和风水格局
- 提供家具摆放和装饰建议
- 解答风水相关问题
- 给出风水改善方案

💡 **回答原则**：
- 每次回答控制在200字以内，简洁有力
- 重点突出，避免过于复杂的术语
- 如需详细分析，可以分步骤说明
- 始终保持专业和友善的态度

请用中文与用户交流，提供专业的风水咨询服务。"""
            }
        ]
        
        print("🏮 风水AI助手控制台版本")
        print("=" * 40)
        print("👋 您好！我是张大师，您的专业风水顾问。")
        print("💬 请输入您的风水问题，输入 'quit' 或 'exit' 退出。")
        print("=" * 40)
    
    async def get_response(self, user_input: str) -> str:
        """获取AI回复"""
        
        # 添加用户消息到历史
        self.conversation_history.append({
            "role": "user",
            "content": user_input
        })
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    "https://api.deepseek.com/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                    },
                    json={
                        "model": "deepseek-chat",
                        "messages": self.conversation_history,
                        "stream": False,
                        "temperature": 0.7,
                        "max_tokens": 500
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if "choices" in result and len(result["choices"]) > 0:
                        ai_response = result["choices"][0]["message"]["content"]
                        
                        # 添加AI回复到历史
                        self.conversation_history.append({
                            "role": "assistant",
                            "content": ai_response
                        })
                        
                        return ai_response
                    else:
                        return "抱歉，我现在无法回答您的问题，请稍后再试。"
                else:
                    return f"服务暂时不可用，请稍后再试。(错误码: {response.status_code})"
                    
        except Exception as e:
            return f"连接出现问题，请检查网络连接。错误: {str(e)}"
    
    async def start_conversation(self):
        """开始对话循环"""
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n🙋 您: ").strip()
                
                # 检查退出命令
                if user_input.lower() in ['quit', 'exit', '退出', '再见']:
                    print("\n👋 张大师: 感谢您的咨询，祝您家宅平安，事业顺利！")
                    break
                
                # 检查空输入
                if not user_input:
                    print("💡 请输入您的风水问题...")
                    continue
                
                # 显示思考状态
                print("🤔 张大师正在思考...")
                
                # 获取AI回复
                response = await self.get_response(user_input)
                
                # 显示回复
                print(f"\n🧙 张大师: {response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 张大师: 感谢您的咨询，祝您家宅平安，事业顺利！")
                break
            except Exception as e:
                print(f"\n❌ 出现错误: {e}")
                print("💡 请重试或输入 'quit' 退出")

async def main():
    """主函数"""
    try:
        bot = FengshuiConsoleBot()
        await bot.start_conversation()
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        print("💡 请检查.env文件中的DEEPSEEK_API_KEY配置")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
