#!/usr/bin/env python3
"""
测试所有API服务的连接状态
"""

import os
import asyncio
import json
from dotenv import load_dotenv
import httpx

load_dotenv()

async def test_deepseek_api():
    """测试DeepSeek API"""
    print("🧠 测试DeepSeek API...")
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ 未找到DEEPSEEK_API_KEY")
        return False
    
    try:
        async with httpx.AsyncClient(timeout=15.0) as client:
            response = await client.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                },
                json={
                    "model": "deepseek-chat",
                    "messages": [{"role": "user", "content": "你好"}],
                    "max_tokens": 50
                }
            )
            
            if response.status_code == 200:
                print("✅ DeepSeek API - 正常")
                return True
            else:
                print(f"❌ DeepSeek API - 错误: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ DeepSeek API - 异常: {e}")
        return False

async def test_deepgram_api():
    """测试Deepgram API"""
    print("🎤 测试Deepgram API...")
    
    api_key = os.getenv("DEEPGRAM_API_KEY")
    if not api_key:
        print("❌ 未找到DEEPGRAM_API_KEY")
        return False
    
    try:
        async with httpx.AsyncClient(timeout=15.0) as client:
            # 测试获取项目信息
            response = await client.get(
                "https://api.deepgram.com/v1/projects",
                headers={
                    "Authorization": f"Token {api_key}",
                }
            )
            
            if response.status_code == 200:
                print("✅ Deepgram API - 正常")
                return True
            else:
                print(f"❌ Deepgram API - 错误: {response.status_code}")
                print(f"响应: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Deepgram API - 异常: {e}")
        return False

async def test_cartesia_api():
    """测试Cartesia API"""
    print("🔊 测试Cartesia API...")
    
    api_key = os.getenv("CARTESIA_API_KEY")
    if not api_key:
        print("❌ 未找到CARTESIA_API_KEY")
        return False
    
    try:
        async with httpx.AsyncClient(timeout=15.0) as client:
            # 测试获取声音列表
            response = await client.get(
                "https://api.cartesia.ai/voices",
                headers={
                    "X-API-Key": api_key,
                    "Cartesia-Version": "2024-06-10"
                }
            )
            
            if response.status_code == 200:
                print("✅ Cartesia API - 正常")
                return True
            else:
                print(f"❌ Cartesia API - 错误: {response.status_code}")
                print(f"响应: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Cartesia API - 异常: {e}")
        return False

def test_livekit_config():
    """测试LiveKit配置"""
    print("🎥 检查LiveKit配置...")
    
    api_key = os.getenv("LIVEKIT_API_KEY")
    api_secret = os.getenv("LIVEKIT_API_SECRET")
    url = os.getenv("LIVEKIT_URL")
    
    if not api_key:
        print("❌ 未找到LIVEKIT_API_KEY")
        return False
    
    if not api_secret:
        print("⚠️ 未找到LIVEKIT_API_SECRET (需要从LiveKit Cloud获取)")
        print("💡 请登录 https://cloud.livekit.io/ 获取完整的API Secret")
        return False
    
    if not url:
        print("❌ 未找到LIVEKIT_URL")
        return False
    
    print(f"✅ LiveKit URL: {url}")
    print(f"✅ LiveKit API Key: {api_key[:10]}...")
    print(f"✅ LiveKit API Secret: {api_secret[:10]}...")
    
    return True

async def test_livekit_connection():
    """测试LiveKit连接"""
    print("🔗 测试LiveKit连接...")
    
    url = os.getenv("LIVEKIT_URL")
    if not url:
        print("❌ 未找到LIVEKIT_URL")
        return False
    
    try:
        # 将wss://替换为https://来测试HTTP连接
        http_url = url.replace("wss://", "https://")
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{http_url}/")
            
            if response.status_code in [200, 404, 405]:  # 这些都表示服务器可达
                print("✅ LiveKit服务器 - 可达")
                return True
            else:
                print(f"⚠️ LiveKit服务器 - 状态码: {response.status_code}")
                return True  # 仍然认为是可达的
                
    except Exception as e:
        print(f"❌ LiveKit服务器 - 连接失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 开始测试所有API服务连接")
    print("=" * 50)
    
    results = {}
    
    # 测试所有API
    results["deepseek"] = await test_deepseek_api()
    print()
    
    results["deepgram"] = await test_deepgram_api()
    print()
    
    results["cartesia"] = await test_cartesia_api()
    print()
    
    results["livekit_config"] = test_livekit_config()
    print()
    
    results["livekit_connection"] = await test_livekit_connection()
    print()
    
    # 总结结果
    print("📊 测试结果总结:")
    print("=" * 30)
    
    all_passed = True
    for service, passed in results.items():
        status = "✅ 正常" if passed else "❌ 失败"
        print(f"{service.upper()}: {status}")
        if not passed:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有服务测试通过！可以启动LiveKit Agent")
        print()
        print("🚀 下一步:")
        print("1. 等待模型下载完成")
        print("2. 运行: python agent.py console")
        print("3. 测试语音对话功能")
    else:
        print("⚠️ 部分服务需要配置，请检查上述错误信息")
        print()
        print("🔧 常见问题:")
        print("1. 检查API密钥是否正确")
        print("2. 确认网络连接正常")
        print("3. 验证API密钥是否有效且有余额")

if __name__ == "__main__":
    asyncio.run(main())
