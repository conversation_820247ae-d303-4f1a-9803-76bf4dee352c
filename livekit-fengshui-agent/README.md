# 🏮 LiveKit风水AI助手

基于LiveKit Agents v1.2.2官方架构构建的专业风水咨询AI助手，使用DeepSeek替代OpenAI，专为中国用户优化。

## 🎯 项目特点

- ✅ **完全按照LiveKit官方指南构建** - 使用最新v1.2.2版本
- ✅ **DeepSeek集成** - 替代OpenAI，中文理解优秀
- ✅ **专业风水知识** - 30年经验的风水大师人设
- ✅ **多模态支持** - 语音、文本、视频交互
- ✅ **实时对话** - 低延迟语音交互
- ✅ **生产就绪** - 可直接部署到生产环境

## 🏗️ 技术架构

```
用户 → LiveKit → Agent Session → AI组件
                                ├── Deepgram STT (语音识别)
                                ├── DeepSeek LLM (对话生成)
                                ├── Cartesia TTS (语音合成)
                                ├── Silero VAD (语音检测)
                                └── Turn Detector (转换检测)
```

## 📋 环境要求

- Python 3.9+
- 4核4GB服务器 (最低配置)
- 稳定的网络连接

## 🚀 快速开始

### 1. 安装依赖

```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装LiveKit Agents
pip install "livekit-agents[deepgram,openai,cartesia,silero,turn-detector]~=1.0" \
            "livekit-plugins-noise-cancellation~=0.2" \
            "python-dotenv"
```

### 2. 配置环境变量

复制 `.env` 文件并填入您的API密钥：

```bash
# DeepSeek API (已配置您的密钥)
DEEPSEEK_API_KEY=***********************************

# 需要申请的API密钥
DEEPGRAM_API_KEY=your-deepgram-api-key
CARTESIA_API_KEY=your-cartesia-api-key

# LiveKit服务器配置
LIVEKIT_API_KEY=your-livekit-api-key
LIVEKIT_API_SECRET=your-livekit-api-secret
LIVEKIT_URL=wss://your-project.livekit.cloud
```

### 3. 下载模型文件

```bash
python agent.py download-files
```

### 4. 运行Agent

#### 控制台模式 (本地测试)
```bash
python agent.py console
```

#### 开发模式 (连接LiveKit)
```bash
python agent.py dev
```

#### 生产模式
```bash
python agent.py start
```

## 🔑 API密钥申请指南

### DeepSeek API ✅
- **状态**: 已配置
- **用途**: 大语言模型，替代OpenAI
- **优势**: 中文理解优秀，成本低

### Deepgram API 🔄
- **申请地址**: https://deepgram.com/
- **用途**: 语音识别 (STT)
- **免费额度**: 每月200小时
- **优势**: 支持中文，准确率高

### Cartesia API 🔄
- **申请地址**: https://cartesia.ai/
- **用途**: 语音合成 (TTS)
- **免费额度**: 每月1000次调用
- **优势**: 声音自然，支持多语言

### LiveKit Cloud 🔄
- **申请地址**: https://cloud.livekit.io/
- **用途**: 实时音视频传输
- **免费额度**: 每月50GB流量
- **优势**: 低延迟，高质量

## 🎭 风水助手功能

### 专业服务
- 🏠 **住宅风水分析** - 房屋布局、朝向分析
- 🏢 **商业风水咨询** - 办公室、店铺布局
- ⚰️ **墓地风水指导** - 阴宅选址建议
- 🎨 **装饰风水建议** - 颜色、摆设指导

### 交互方式
- 🗣️ **语音对话** - 自然语音交流
- 💬 **文字聊天** - 文本消息交互
- 📷 **图片分析** - 上传房屋图片分析
- 🎥 **视频通话** - 实时视频咨询

## 📊 性能表现

### 4核4GB服务器
- **响应延迟**: 1-2秒
- **并发用户**: 5-15人
- **功能完整度**: 80%
- **稳定性**: 良好

### 推荐配置 (8核16GB)
- **响应延迟**: 0.5-1秒
- **并发用户**: 50-100人
- **功能完整度**: 100%
- **稳定性**: 优秀

## 🔧 自定义配置

### 修改风水大师人设
编辑 `.env` 文件：
```bash
FENGSHUI_MASTER_NAME=李大师
FENGSHUI_EXPERTISE=住宅风水,商业风水,择日选址
```

### 调整AI参数
编辑 `agent.py` 中的DeepSeek配置：
```python
deepseek_llm = DeepSeekLLM(
    model="deepseek-chat",
    temperature=0.7,  # 创造性 (0-1)
    max_tokens=2000,  # 最大回复长度
)
```

## 🚀 部署到生产环境

### Docker部署
```bash
# 构建镜像
docker build -t fengshui-agent .

# 运行容器
docker run -d --name fengshui-agent \
  --env-file .env \
  -p 8080:8080 \
  fengshui-agent
```

### 云服务器部署
```bash
# 使用systemd管理服务
sudo cp fengshui-agent.service /etc/systemd/system/
sudo systemctl enable fengshui-agent
sudo systemctl start fengshui-agent
```

## 📝 开发说明

### 项目结构
```
livekit-fengshui-agent/
├── agent.py              # 主要Agent代码
├── .env                  # 环境变量配置
├── requirements.txt      # Python依赖 (自动生成)
├── README.md            # 项目说明
├── venv/                # 虚拟环境
└── logs/                # 日志文件 (运行时生成)
```

### 核心组件
- **FengshuiAssistant**: 风水助手Agent类
- **DeepSeekLLM**: DeepSeek API集成
- **entrypoint**: LiveKit Agent入口函数

## 🐛 故障排除

### 常见问题

1. **模型下载失败**
   ```bash
   # 手动下载模型
   python agent.py download-files
   ```

2. **API密钥错误**
   ```bash
   # 检查.env文件配置
   cat .env
   ```

3. **网络连接问题**
   ```bash
   # 测试网络连接
   curl -I https://api.deepseek.com
   ```

## 📞 技术支持

- **LiveKit官方文档**: https://docs.livekit.io/agents/
- **DeepSeek API文档**: https://platform.deepseek.com/api-docs/
- **项目Issues**: 请在GitHub提交问题

## 📄 许可证

本项目基于MIT许可证开源，可自由用于商业用途。
