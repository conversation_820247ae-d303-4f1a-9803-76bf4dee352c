"""
LiveKit风水AI助手 - 官方架构 + DeepSeek集成
按照LiveKit官方指南构建，使用DeepSeek替代OpenAI
"""

import os
import asyncio
import json
from typing import AsyncIterator, Optional
from dotenv import load_dotenv

from livekit import agents
from livekit.agents import AgentSession, Agent, RoomInputOptions
from livekit.plugins import (
    cartesia,
    deepgram,
    noise_cancellation,
    silero,
)
from livekit.plugins.turn_detector.multilingual import MultilingualModel

# 导入HTTP客户端用于DeepSeek API
import httpx

load_dotenv()


class DeepSeekLLM:
    """DeepSeek LLM集成，替代OpenAI"""
    
    def __init__(
        self,
        model: str = "deepseek-chat",
        api_key: Optional[str] = None,
        base_url: str = "https://api.deepseek.com/v1",
        temperature: float = 0.7,
        max_tokens: int = 2000,
    ):
        self.model = model
        self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        self.base_url = base_url
        self.temperature = temperature
        self.max_tokens = max_tokens
        
        if not self.api_key:
            raise ValueError("DEEPSEEK_API_KEY environment variable is required")
        
        print(f"✅ 初始化DeepSeek LLM: {model}")
    
    async def generate_response(self, messages: list) -> AsyncIterator[str]:
        """生成流式响应"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 构建请求数据
                request_data = {
                    "model": self.model,
                    "messages": messages,
                    "stream": True,
                    "temperature": self.temperature,
                    "max_tokens": self.max_tokens,
                }
                
                # 发送请求
                async with client.stream(
                    "POST",
                    f"{self.base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                    },
                    json=request_data,
                ) as response:
                    
                    if response.status_code != 200:
                        error_text = await response.aread()
                        print(f"❌ DeepSeek API错误: {response.status_code} - {error_text}")
                        yield "抱歉，AI服务暂时不可用，请稍后再试。"
                        return
                    
                    # 处理流式响应
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]  # 移除"data: "前缀
                            
                            if data.strip() == "[DONE]":
                                break
                            
                            try:
                                chunk = json.loads(data)
                                
                                if "choices" in chunk and len(chunk["choices"]) > 0:
                                    choice = chunk["choices"][0]
                                    
                                    # 处理内容增量
                                    if "delta" in choice and "content" in choice["delta"]:
                                        content = choice["delta"]["content"]
                                        if content:
                                            yield content
                            
                            except json.JSONDecodeError:
                                continue
                            except Exception as e:
                                print(f"❌ 处理DeepSeek响应时出错: {e}")
                                continue
        
        except Exception as e:
            print(f"❌ DeepSeek API调用失败: {e}")
            yield f"抱歉，AI服务暂时不可用，请稍后再试。错误信息: {str(e)}"


class FengshuiAssistant(Agent):
    """风水AI助手 - 基于LiveKit官方架构"""
    
    def __init__(self) -> None:
        # 风水大师人设和指令
        master_name = os.getenv("FENGSHUI_MASTER_NAME", "张大师")
        expertise = os.getenv("FENGSHUI_EXPERTISE", "住宅风水,商业风水,墓地风水")
        
        instructions = f"""
你是{master_name}，一位拥有30年经验的专业风水大师。

🎯 **你的专长**：{expertise}

🗣️ **对话风格**：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释复杂的风水概念
- 结合传统风水理论和现代生活实际
- 给出具体可行的建议

📋 **服务内容**：
- 分析房屋布局和风水格局
- 提供家具摆放和装饰建议
- 解答风水相关问题
- 给出风水改善方案

💡 **回答原则**：
- 每次回答控制在200字以内，简洁有力
- 重点突出，避免过于复杂的术语
- 如需详细分析，可以分步骤说明
- 始终保持专业和友善的态度

请用中文与用户交流，提供专业的风水咨询服务。
"""
        
        super().__init__(instructions=instructions)
        print(f"✅ 初始化风水助手: {master_name}")


async def entrypoint(ctx: agents.JobContext):
    """LiveKit Agent入口点 - 官方标准架构"""
    
    print("🏮 启动LiveKit风水AI助手")
    
    # 检查必要的环境变量
    required_env_vars = [
        "DEEPSEEK_API_KEY",
        "DEEPGRAM_API_KEY", 
        "CARTESIA_API_KEY"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        print(f"❌ 缺少必要的环境变量: {missing_vars}")
        print("💡 请在.env文件中设置这些环境变量")
        return
    
    # 初始化AI组件 - 按照官方指南
    try:
        # Deepgram STT (语音识别)
        stt = deepgram.STT(
            model="nova-3", 
            language="zh",  # 中文优化
        )
        print("✅ Deepgram STT初始化成功")
        
        # Cartesia TTS (语音合成)
        tts = cartesia.TTS(
            model="sonic-2",
            voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 可以选择中文声音
            language="zh"  # 中文语音
        )
        print("✅ Cartesia TTS初始化成功")
        
        # Silero VAD (语音活动检测)
        vad = silero.VAD.load()
        print("✅ Silero VAD初始化成功")
        
        # 多语言转换检测
        turn_detection = MultilingualModel()
        print("✅ 多语言转换检测初始化成功")
        
        # DeepSeek LLM (替代OpenAI)
        deepseek_llm = DeepSeekLLM(
            model="deepseek-chat",
            temperature=0.7,
            max_tokens=2000
        )
        
    except Exception as e:
        print(f"❌ AI组件初始化失败: {e}")
        return
    
    # 创建Agent会话 - 官方标准架构
    session = AgentSession(
        stt=stt,
        llm=None,  # 我们将使用自定义的DeepSeek LLM
        tts=tts,
        vad=vad,
        turn_detection=turn_detection,
    )
    
    # 配置房间输入选项
    room_input_options = RoomInputOptions()
    
    # 如果使用LiveKit Cloud，启用噪音消除
    if os.getenv("LIVEKIT_API_KEY"):
        room_input_options.noise_cancellation = noise_cancellation.BVC()
        print("✅ 启用LiveKit Cloud噪音消除")
    
    # 启动会话
    await session.start(
        room=ctx.room,
        agent=FengshuiAssistant(),
        room_input_options=room_input_options,
    )
    
    # 生成欢迎消息
    welcome_message = """
🏮 您好！我是您的专业风水顾问助手。

我可以为您提供：
• 房屋布局风水分析
• 家具摆放建议  
• 颜色搭配指导
• 风水改善方案

请告诉我您想咨询什么风水问题，或者描述一下您的房屋情况。
"""
    
    await session.generate_reply(
        instructions=f"用温和专业的语气说：{welcome_message}"
    )
    
    print("🎯 风水AI助手已就绪，等待用户交互")


if __name__ == "__main__":
    # 运行LiveKit Agent
    agents.cli.run_app(
        agents.WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=lambda: print("🔥 预热风水AI助手...")
        )
    )
