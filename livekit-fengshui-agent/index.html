<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏮 LiveKit风水AI助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 800px;
            margin: 20px;
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2.5rem;
        }
        
        .subtitle {
            color: #7f8c8d;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        
        .status {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status h3 {
            color: #2e7d32;
            margin-bottom: 15px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .feature h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .commands {
            background: #f1f3f4;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .commands h3 {
            text-align: center;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .command {
            background: #2c3e50;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        
        .links {
            margin-top: 30px;
        }
        
        .links a {
            display: inline-block;
            margin: 10px;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .footer {
            margin-top: 30px;
            color: #7f8c8d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🏮</div>
        <h1>LiveKit风水AI助手</h1>
        <p class="subtitle">专业的AI风水顾问，为您提供传统风水智慧与现代科技的完美结合</p>
        
        <div class="status">
            <h3>✅ 系统状态：已部署完成</h3>
            <p>🔐 SSL证书：有效期至 2025-10-28</p>
            <p>🧠 AI模型：DeepSeek Chat (中文优化)</p>
            <p>🎤 语音识别：Deepgram Nova-3</p>
            <p>🔊 语音合成：Cartesia Sonic-2</p>
            <p>🎥 实时通信：LiveKit Cloud</p>
        </div>
        
        <div class="feature-grid">
            <div class="feature">
                <h4>🏠 住宅风水</h4>
                <p>房屋布局、朝向分析、家具摆放建议</p>
            </div>
            <div class="feature">
                <h4>🏢 商业风水</h4>
                <p>办公室、店铺风水布局优化</p>
            </div>
            <div class="feature">
                <h4>⚰️ 墓地风水</h4>
                <p>阴宅选址、风水格局指导</p>
            </div>
            <div class="feature">
                <h4>🎨 装饰风水</h4>
                <p>颜色搭配、装饰品摆放建议</p>
            </div>
        </div>
        
        <div class="commands">
            <h3>🚀 快速启动命令</h3>
            <div class="command">cd /www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent</div>
            <div class="command">source venv/bin/activate</div>
            <div class="command">python console_test.py  # 控制台模式</div>
            <div class="command">python agent.py dev     # 开发模式</div>
            <div class="command">python agent.py start   # 生产模式</div>
        </div>
        
        <div class="links">
            <a href="/docs/README.md" target="_blank">📖 项目文档</a>
            <a href="/docs/FINAL_DEPLOYMENT_REPORT.md" target="_blank">📊 部署报告</a>
            <a href="https://docs.livekit.io/agents/" target="_blank">🔗 LiveKit文档</a>
        </div>
        
        <div class="footer">
            <p>🎯 基于LiveKit Agents v1.2.2官方架构构建</p>
            <p>💡 使用DeepSeek替代OpenAI，专为中文用户优化</p>
            <p>🔒 SSL证书由Let's Encrypt提供，安全可靠</p>
        </div>
    </div>
</body>
</html>
