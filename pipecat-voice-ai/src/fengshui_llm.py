"""
DeepSeek LLM集成 - 替代OpenAI，专为风水咨询优化
"""

import asyncio
import json
import os
from typing import AsyncIterator, Optional, Dict, Any

import httpx
from livekit.agents import llm
from loguru import logger


class DeepSeekLLM(llm.LLM):
    """DeepSeek LLM集成，替代OpenAI"""
    
    def __init__(
        self,
        *,
        model: str = "deepseek-chat",
        api_key: Optional[str] = None,
        base_url: str = "https://api.deepseek.com/v1",
        temperature: float = 0.7,
        max_tokens: int = 2000,
        **kwargs
    ):
        super().__init__(**kwargs)
        
        self.model = model
        self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        self.base_url = base_url
        self.temperature = temperature
        self.max_tokens = max_tokens
        
        if not self.api_key:
            raise ValueError("DEEPSEEK_API_KEY environment variable is required")
        
        # 风水专用系统提示词
        self.system_prompt = """
你是一位资深的风水大师，拥有30年的专业风水咨询经验。你的专长包括：

🏠 **住宅风水**：
- 房屋选址和朝向分析
- 客厅、卧室、厨房、卫生间布局
- 家具摆放和装饰建议
- 颜色搭配和材质选择

🏢 **商业风水**：
- 办公室和店铺布局
- 财位识别和布置
- 招财旺运的方法

⚰️ **阴宅风水**：
- 墓地选址和朝向
- 祖坟对后代的影响

📐 **风水理论**：
- 八卦方位和五行相生相克
- 风水化煞和调理方法
- 现代风水与传统理论结合

请用专业但易懂的语言回答用户的风水问题，结合传统风水理论和现代生活实际，给出实用的建议。
回答要简洁明了，重点突出，避免过于复杂的术语。
"""
        
        logger.info(f"初始化DeepSeek LLM: {model}")
    
    async def agenerate(
        self,
        chat_ctx: llm.ChatContext,
        tools: Optional[list] = None,
    ) -> llm.LLMStream:
        """生成LLM响应流"""
        
        # 构建消息历史
        messages = [{"role": "system", "content": self.system_prompt}]
        
        for msg in chat_ctx.messages:
            if msg.role == "user":
                messages.append({"role": "user", "content": msg.content})
            elif msg.role == "assistant":
                messages.append({"role": "assistant", "content": msg.content})
        
        logger.debug(f"发送到DeepSeek的消息数量: {len(messages)}")
        
        # 调用DeepSeek API
        return self._create_stream(messages, tools)
    
    def _create_stream(
        self,
        messages: list,
        tools: Optional[list] = None
    ) -> llm.LLMStream:
        """创建流式响应"""
        
        async def _stream_generator() -> AsyncIterator[str]:
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    # 构建请求数据
                    request_data = {
                        "model": self.model,
                        "messages": messages,
                        "stream": True,
                        "temperature": self.temperature,
                        "max_tokens": self.max_tokens,
                    }
                    
                    # 如果有函数调用上下文，添加工具定义
                    if fnc_ctx and fnc_ctx.ai_functions:
                        tools = []
                        for func in fnc_ctx.ai_functions:
                            tools.append({
                                "type": "function",
                                "function": {
                                    "name": func.name,
                                    "description": func.description,
                                    "parameters": func.parameters
                                }
                            })
                        request_data["tools"] = tools
                        request_data["tool_choice"] = "auto"
                    
                    # 发送请求
                    async with client.stream(
                        "POST",
                        f"{self.base_url}/chat/completions",
                        headers={
                            "Authorization": f"Bearer {self.api_key}",
                            "Content-Type": "application/json",
                        },
                        json=request_data,
                    ) as response:
                        
                        if response.status_code != 200:
                            error_text = await response.aread()
                            logger.error(f"DeepSeek API错误: {response.status_code} - {error_text}")
                            raise Exception(f"DeepSeek API错误: {response.status_code}")
                        
                        # 处理流式响应
                        async for line in response.aiter_lines():
                            if line.startswith("data: "):
                                data = line[6:]  # 移除"data: "前缀
                                
                                if data.strip() == "[DONE]":
                                    break
                                
                                try:
                                    chunk = json.loads(data)
                                    
                                    if "choices" in chunk and len(chunk["choices"]) > 0:
                                        choice = chunk["choices"][0]
                                        
                                        # 处理内容增量
                                        if "delta" in choice and "content" in choice["delta"]:
                                            content = choice["delta"]["content"]
                                            if content:
                                                yield content
                                        
                                        # 处理函数调用
                                        if "delta" in choice and "tool_calls" in choice["delta"]:
                                            tool_calls = choice["delta"]["tool_calls"]
                                            if tool_calls and fnc_ctx:
                                                for tool_call in tool_calls:
                                                    if "function" in tool_call:
                                                        func_name = tool_call["function"]["name"]
                                                        func_args = json.loads(tool_call["function"]["arguments"])
                                                        
                                                        # 执行函数调用
                                                        if func_name in [f.name for f in fnc_ctx.ai_functions]:
                                                            result = await fnc_ctx.call_function(func_name, **func_args)
                                                            yield f"\n[执行了{func_name}函数，结果: {result}]\n"
                                
                                except json.JSONDecodeError:
                                    continue
                                except Exception as e:
                                    logger.error(f"处理DeepSeek响应时出错: {e}")
                                    continue
            
            except Exception as e:
                logger.error(f"DeepSeek API调用失败: {e}")
                yield f"抱歉，AI服务暂时不可用，请稍后再试。错误信息: {str(e)}"
        
        return llm.LLMStream(_stream_generator())


class FengshuiKnowledgeBase:
    """风水知识库"""
    
    @staticmethod
    def get_direction_info(direction: str) -> str:
        """获取方位信息"""
        directions = {
            "东": "东方属木，代表健康和家庭，适合放置绿植",
            "南": "南方属火，代表名声和事业，适合红色装饰",
            "西": "西方属金，代表子女和创意，适合金属装饰",
            "北": "北方属水，代表事业和智慧，适合蓝黑色装饰",
            "东南": "东南方为巽卦，代表财运，适合放置水元素",
            "西南": "西南方为坤卦，代表母亲和大地，适合厚重装饰",
            "西北": "西北方为乾卦，代表父亲和权威，适合金属圆形物品",
            "东北": "东北方为艮卦，代表知识和智慧，适合放置书籍"
        }
        return directions.get(direction, "请提供具体的方位信息")
    
    @staticmethod
    def get_room_advice(room_type: str) -> str:
        """获取房间建议"""
        rooms = {
            "客厅": "客厅是家庭的核心，应保持明亮整洁，沙发背靠实墙面向门口",
            "卧室": "卧室应安静私密，床头靠墙，避免镜子对床，颜色宜温和",
            "厨房": "厨房代表财运，应保持清洁，炉灶不对门，通风良好",
            "卫生间": "卫生间应通风良好，门常关，避免对着其他房间门",
            "书房": "书房应安静明亮，书桌面向门口但不正对门，利于学习"
        }
        return rooms.get(room_type, "请提供具体的房间类型")


# 导出主要类
__all__ = ["DeepSeekLLM", "FengshuiKnowledgeBase"]
