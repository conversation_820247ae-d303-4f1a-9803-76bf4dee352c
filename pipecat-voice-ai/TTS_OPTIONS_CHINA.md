# 中国免费TTS语音合成服务对比

## 🎯 主要选项

### 1. 智谱AI (GLM-4-Voice) ⭐⭐⭐⭐⭐
- **状态**: 最新推出的端到端语音模型
- **特点**: 
  - 情感语音合成，支持停顿、喘气等细节
  - 中英文双语支持
  - 端到端处理，质量很高
- **免费额度**: 需要查看官方文档
- **API**: 支持
- **推荐指数**: ⭐⭐⭐⭐⭐ (最新技术)

### 2. 科大讯飞 ⭐⭐⭐⭐
- **免费额度**: 每日500次调用
- **特点**: 
  - 中文语音合成领域领先
  - 多种音色选择
  - 支持SSML标记
- **API**: 成熟稳定
- **推荐指数**: ⭐⭐⭐⭐ (中文优秀)

### 3. 百度AI开放平台 ⭐⭐⭐⭐
- **免费额度**: 每月50万字符
- **特点**:
  - 多种音色和语速
  - 支持情感合成
  - 文档完善
- **API**: 简单易用
- **推荐指数**: ⭐⭐⭐⭐ (额度充足)

### 4. 腾讯云语音合成 ⭐⭐⭐
- **免费额度**: 每月10万字符
- **特点**:
  - 音质清晰
  - 多种音色
- **API**: 标准REST API
- **推荐指数**: ⭐⭐⭐ (额度一般)

### 5. 阿里云语音合成 ⭐⭐⭐
- **免费额度**: 每月2万次调用
- **特点**:
  - 多种音色
  - 支持自定义音色
- **API**: 阿里云SDK
- **推荐指数**: ⭐⭐⭐ (额度较少)

### 6. ChatTTS (开源) ⭐⭐⭐⭐⭐
- **状态**: 完全免费开源
- **特点**:
  - 本地部署
  - 对话式TTS
  - 支持中英文
- **部署**: 需要GPU资源
- **推荐指数**: ⭐⭐⭐⭐⭐ (免费无限制)

## 🚀 推荐方案

### 方案1: 智谱AI GLM-4-Voice (推荐)
```python
# 智谱AI语音合成
# 需要申请API密钥
ZHIPU_API_KEY = "your_zhipu_api_key"
```

### 方案2: 百度AI (备选)
```python
# 百度语音合成
BAIDU_APP_ID = "your_app_id"
BAIDU_API_KEY = "your_api_key" 
BAIDU_SECRET_KEY = "your_secret_key"
```

### 方案3: 科大讯飞 (备选)
```python
# 讯飞语音合成
XUNFEI_APP_ID = "your_app_id"
XUNFEI_API_KEY = "your_api_key"
XUNFEI_API_SECRET = "your_api_secret"
```

### 方案4: ChatTTS本地部署 (无限制)
```bash
# 本地部署ChatTTS
git clone https://github.com/2noise/ChatTTS.git
cd ChatTTS
pip install -r requirements.txt
```

## 📋 申请步骤

### 智谱AI申请
1. 访问: https://www.zhipuai.cn/
2. 注册账号
3. 申请API密钥
4. 查看GLM-4-Voice文档

### 百度AI申请
1. 访问: https://ai.baidu.com/
2. 创建应用
3. 获取APP_ID、API_KEY、SECRET_KEY
4. 每月50万字符免费额度

### 科大讯飞申请
1. 访问: https://www.xfyun.cn/
2. 创建应用
3. 获取APPID、API_KEY、API_SECRET
4. 每日500次免费调用

## 🔧 集成建议

### 优先级排序:
1. **智谱AI GLM-4-Voice** - 最新技术，情感语音
2. **百度AI** - 免费额度最大，稳定可靠
3. **科大讯飞** - 中文语音质量优秀
4. **ChatTTS** - 完全免费，需要本地部署

### 多重备选方案:
```python
# 按优先级尝试不同TTS服务
TTS_PROVIDERS = [
    "zhipu",      # 智谱AI
    "baidu",      # 百度AI  
    "xunfei",     # 科大讯飞
    "chattts"     # 本地ChatTTS
]
```

## 💡 建议

1. **首选智谱AI**: 最新的GLM-4-Voice技术最先进
2. **备选百度**: 免费额度最大，适合开发测试
3. **本地ChatTTS**: 如果有GPU资源，完全免费
4. **多服务集成**: 实现自动切换，提高可用性

## ✅ 已完成集成

### 智谱AI TTS服务已集成 🎉

我们已经成功将智谱AI TTS服务集成到pipecat项目中：

1. **✅ 创建了自定义TTS服务类** (`zhipu_tts_service.py`)
2. **✅ 配置了环境变量** (`.env`文件中的`ZHIPU_API_KEY`)
3. **✅ 更新了主要语音机器人文件** (`voice_bot_https.py`)
4. **✅ 通过了完整测试** (`test_zhipu_tts.py`)

### 使用方法

#### 1. 配置API密钥
在`.env`文件中设置：
```bash
ZHIPU_API_KEY=f0cb388b4b194279aef8897d4fa1bd95.PXSo4bqG2vNkbrF7
```

#### 2. 选择TTS提供商
在`.env`文件中设置：
```bash
TTS_PROVIDER=zhipu  # 使用智谱AI TTS
```

#### 3. 启动语音机器人
```bash
python voice_bot_https.py
```

### 测试结果 ✅

- **API连接**: 成功
- **TTS服务创建**: 成功
- **语音合成**: 成功生成音频数据
- **多文本测试**: 全部通过
- **音频数据大小**: 正常 (33KB-72KB)

## 🎯 下一步行动

1. ✅ ~~申请智谱AI API密钥~~ (已完成)
2. ✅ ~~集成到pipecat项目中~~ (已完成)
3. ✅ ~~测试语音合成效果~~ (已完成)
4. 🔄 优化语音质量和性能
5. 🔄 添加更多语音选项和参数调节
