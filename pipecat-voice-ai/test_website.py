#!/usr/bin/env python3

"""
测试pipecat语音机器人网站
使用Playwright检查网站状态和功能
"""

import asyncio
import ssl
from playwright.async_api import async_playwright

async def test_website():
    """测试网站基本功能"""
    print("🔍 开始测试pipecat语音机器人网站...")
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context(
            ignore_https_errors=True,  # 忽略SSL证书错误
            permissions=['microphone']  # 授予麦克风权限
        )
        page = await context.new_page()
        
        try:
            # 测试HTTPS连接
            print("📡 测试HTTPS连接...")
            url = "https://su.guiyunai.fun:7860"
            
            response = await page.goto(url, timeout=30000)
            print(f"✅ 网站响应状态: {response.status}")
            
            # 检查页面标题
            title = await page.title()
            print(f"📄 页面标题: {title}")
            
            # 检查页面内容
            content = await page.content()
            if "Connect" in content or "连接" in content:
                print("✅ 找到连接按钮")
            else:
                print("❌ 未找到连接按钮")
            
            # 检查是否有WebRTC相关元素
            webrtc_elements = await page.query_selector_all('[data-testid*="webrtc"], [class*="webrtc"], [id*="webrtc"]')
            if webrtc_elements:
                print(f"✅ 找到 {len(webrtc_elements)} 个WebRTC相关元素")
            else:
                print("⚠️ 未找到WebRTC相关元素")
            
            # 检查JavaScript错误
            errors = []
            page.on("pageerror", lambda error: errors.append(str(error)))
            
            # 等待页面完全加载
            await page.wait_for_load_state("networkidle", timeout=10000)
            
            if errors:
                print(f"❌ 发现JavaScript错误: {len(errors)}")
                for error in errors[:3]:  # 只显示前3个错误
                    print(f"   - {error}")
            else:
                print("✅ 没有JavaScript错误")
            
            # 检查网络请求
            print("🌐 检查网络请求...")
            requests = []
            page.on("request", lambda request: requests.append(request.url))
            
            # 刷新页面以捕获请求
            await page.reload()
            await page.wait_for_load_state("networkidle", timeout=10000)
            
            print(f"📊 总共发起了 {len(requests)} 个网络请求")
            
            # 检查API请求
            api_requests = [req for req in requests if "/api/" in req]
            if api_requests:
                print(f"✅ 找到 {len(api_requests)} 个API请求")
                for req in api_requests[:3]:
                    print(f"   - {req}")
            else:
                print("⚠️ 未找到API请求")
            
            # 尝试点击连接按钮（如果存在）
            try:
                connect_button = await page.query_selector('button:has-text("Connect"), button:has-text("连接"), [data-testid="connect-button"]')
                if connect_button:
                    print("🔘 尝试点击连接按钮...")
                    await connect_button.click()
                    await page.wait_for_timeout(2000)  # 等待2秒
                    print("✅ 成功点击连接按钮")
                else:
                    print("⚠️ 未找到连接按钮")
            except Exception as e:
                print(f"❌ 点击连接按钮失败: {e}")
            
            # 截图保存
            try:
                await page.screenshot(path="website_screenshot.png")
                print("📸 已保存网站截图: website_screenshot.png")
            except Exception as e:
                print(f"❌ 截图失败: {e}")
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
        
        finally:
            await browser.close()
    
    print("🎉 网站测试完成!")

async def test_api_endpoints():
    """测试API端点"""
    print("\n🔍 测试API端点...")
    
    import aiohttp
    import ssl
    
    # 创建SSL上下文，忽略证书验证
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    
    connector = aiohttp.TCPConnector(ssl=ssl_context)
    
    async with aiohttp.ClientSession(connector=connector) as session:
        # 测试主页
        try:
            async with session.get("https://su.guiyunai.fun:7860/") as response:
                print(f"✅ 主页响应: {response.status}")
        except Exception as e:
            print(f"❌ 主页请求失败: {e}")
        
        # 测试客户端页面
        try:
            async with session.get("https://su.guiyunai.fun:7860/client/") as response:
                print(f"✅ 客户端页面响应: {response.status}")
        except Exception as e:
            print(f"❌ 客户端页面请求失败: {e}")
        
        # 测试API端点（POST请求需要数据，这里只测试是否可达）
        try:
            async with session.post("https://su.guiyunai.fun:7860/api/offer", 
                                   json={"test": "ping"}) as response:
                print(f"✅ API端点响应: {response.status}")
        except Exception as e:
            print(f"❌ API端点请求失败: {e}")

async def main():
    """主函数"""
    await test_website()
    await test_api_endpoints()

if __name__ == "__main__":
    asyncio.run(main())
