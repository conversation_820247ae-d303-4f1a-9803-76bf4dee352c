# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 环境变量
.env
.env.local
.env.production

# 虚拟环境
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
logs/
*.log

# 模型文件
models/
*.pth
*.bin
*.safetensors

# 临时文件
tmp/
temp/
.tmp/

# 系统文件
.DS_Store
Thumbs.db

# 测试覆盖率
.coverage
htmlcov/
.pytest_cache/

# Docker
.dockerignore

# 备份文件
backup-*/
*.bak
