#!/usr/bin/env python3

"""
DeepSeek版中文语音对话机器人

使用DeepSeek API实现完整的STT→LLM→TTS Pipeline
支持中文语音识别、AI对话和语音合成
"""

import argparse
import asyncio
import os
import sys
from typing import Dict

import uvicorn
from dotenv import load_dotenv
from fastapi import BackgroundTasks, FastAPI
from fastapi.responses import RedirectResponse
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.services.openai.stt import OpenAISTTService
from pipecat.services.openai.tts import OpenAITTSService
from pipecat.transports.base_transport import TransportParams
from pipecat.transports.network.small_webrtc import SmallWebRTCTransport
from pipecat.transports.network.webrtc_connection import SmallWebRTCConnection
from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.transcriptions.language import Language

load_dotenv(override=True)


async def run_deepseek_voice_bot(transport, args, handle_sigint: bool):
    """运行DeepSeek版中文语音对话机器人"""
    logger.info("🤖 启动DeepSeek版中文语音对话机器人")

    # 配置语音转文字服务 (STT) - 使用Groq的Whisper
    stt = OpenAISTTService(
        api_key=os.getenv("GROQ_API_KEY"),
        base_url="https://api.groq.com/openai/v1",
        model="whisper-large-v3",
        language=Language.ZH,  # 中文语言设置
    )

    # 配置大语言模型服务 (LLM) - 使用DeepSeek API
    llm = OpenAILLMService(
        api_key=os.getenv("DEEPSEEK_API_KEY"),
        base_url="https://api.deepseek.com",
        model="deepseek-chat",  # DeepSeek的对话模型
    )

    # 配置文字转语音服务 (TTS) - 优先使用Groq TTS
    try:
        from pipecat.services.groq.tts import GroqTTSService
        tts = GroqTTSService(
            api_key=os.getenv("GROQ_API_KEY"),
            voice="alloy"
        )
        logger.info("🔊 使用Groq TTS服务")
    except ImportError:
        # 如果Groq TTS不可用，使用OpenAI TTS
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key and openai_key.strip():
            logger.info("🔊 使用OpenAI TTS服务")
            tts = OpenAITTSService(
                api_key=openai_key,
                voice="alloy",
                model="tts-1",
            )
        else:
            # 最后备选：使用ElevenLabs TTS (如果有API key)
            try:
                from pipecat.services.elevenlabs.tts import ElevenLabsTTSService
                elevenlabs_key = os.getenv("ELEVENLABS_API_KEY")
                if elevenlabs_key and elevenlabs_key.strip():
                    tts = ElevenLabsTTSService(
                        api_key=elevenlabs_key,
                        voice_id="21m00Tcm4TlvDq8ikWAM",  # Rachel voice
                    )
                    logger.info("🔊 使用ElevenLabs TTS服务")
                else:
                    logger.error("❌ 没有可用的TTS服务，请配置API密钥")
                    logger.error("请设置以下任一API密钥:")
                    logger.error("- GROQ_API_KEY (推荐)")
                    logger.error("- OPENAI_API_KEY")
                    logger.error("- ELEVENLABS_API_KEY")
                    sys.exit(1)
            except ImportError:
                logger.error("❌ 没有可用的TTS服务")
                sys.exit(1)

    # 配置LLM上下文管理器 - 中文对话设置
    context = OpenAILLMContext(
        messages=[
            {
                "role": "system",
                "content": """你是一个友好的中文AI语音助手。请遵循以下规则：
1. 用简洁、自然的中文回答用户的问题
2. 回答要简短明了，通常不超过50字
3. 语气要友好、亲切
4. 如果用户问候，要热情回应
5. 如果不理解用户的问题，礼貌地请求澄清
6. 保持对话的连贯性和上下文理解
7. 你的回答会被转换为语音，所以要适合口语表达"""
            }
        ]
    )
    context_aggregator = llm.create_context_aggregator(context)

    # 创建完整的语音处理管道
    pipeline = Pipeline([
        transport.input(),           # WebRTC音频输入
        stt,                        # 语音转文字 (STT) - Groq Whisper
        context_aggregator.user(),  # 用户上下文聚合
        llm,                        # AI语言模型处理 (LLM) - DeepSeek
        tts,                        # 文字转语音 (TTS) - Groq TTS
        transport.output(),         # WebRTC音频输出
        context_aggregator.assistant(),  # 助手上下文聚合
    ])

    task = PipelineTask(pipeline)

    # 当客户端连接时的欢迎处理
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"✅ 客户端已连接: {client}")
        try:
            # 播放中文欢迎消息
            await task.queue_frames([
                TTSSpeakFrame("你好！我是您的中文语音助手，由DeepSeek AI驱动。请开始说话，我会认真听取您的问题并用语音回答。"),
            ])
            logger.info("🎵 DeepSeek欢迎消息已发送")
        except Exception as e:
            logger.error(f"❌ 发送欢迎消息失败: {e}")

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client):
        logger.info(f"❌ 客户端已断开连接: {client}")

    # 添加错误处理
    @transport.event_handler("on_error")
    async def on_error(transport, error):
        logger.error(f"🚨 传输错误: {error}")

    runner = PipelineRunner(handle_sigint=handle_sigint)
    
    logger.info("🔄 开始运行DeepSeek AI语音Pipeline...")
    await runner.run(task)


def create_deepseek_app():
    """创建DeepSeek版FastAPI应用"""
    from pipecat_ai_small_webrtc_prebuilt.frontend import SmallWebRTCPrebuiltUI
    
    app = FastAPI(title="DeepSeek版中文语音对话机器人")
    
    # 存储WebRTC连接
    pcs_map: Dict[str, SmallWebRTCConnection] = {}
    
    # 挂载前端界面
    app.mount("/client", SmallWebRTCPrebuiltUI)
    
    @app.get("/", include_in_schema=False)
    async def root_redirect():
        return RedirectResponse(url="/client/")
    
    @app.post("/api/offer")
    async def offer(request: dict, background_tasks: BackgroundTasks):
        """处理WebRTC offer请求"""
        logger.info(f"📡 收到WebRTC offer请求: {request.get('pc_id', 'unknown')}")
        
        pc_id = request.get("pc_id")
        
        try:
            if pc_id and pc_id in pcs_map:
                # 重用现有连接
                logger.info(f"🔄 重用现有连接: {pc_id}")
                pipecat_connection = pcs_map[pc_id]
                await pipecat_connection.restart(
                    sdp=request["sdp"],
                    type=request["type"],
                    restart_pc=request.get("restart_pc", False),
                )
            else:
                # 创建新连接
                logger.info(f"🆕 创建新的WebRTC连接")
                pipecat_connection = SmallWebRTCConnection()
                await pipecat_connection.initialize(sdp=request["sdp"], type=request["type"])
                
                @pipecat_connection.event_handler("closed")
                async def handle_disconnected(webrtc_connection):
                    logger.info(f"🔌 WebRTC连接已关闭: {webrtc_connection.pc_id}")
                    pcs_map.pop(webrtc_connection.pc_id, None)
                
                # 创建优化的传输参数
                params = TransportParams(
                    audio_out_enabled=True,
                    audio_in_enabled=True,
                    vad_enabled=True,
                    vad_analyzer=SileroVADAnalyzer(),
                    # 音频质量优化
                    audio_out_sample_rate=16000,
                    audio_out_channels=1,
                    # VAD参数优化 - 减少延迟
                    vad_confidence=0.6,  # 降低置信度阈值，更敏感
                    vad_start_secs=0.1,  # 减少开始检测时间
                    vad_stop_secs=0.5,   # 减少停止检测时间
                )
                
                # 创建传输并启动DeepSeek AI机器人
                transport = SmallWebRTCTransport(params=params, webrtc_connection=pipecat_connection)
                
                # 创建模拟的args对象
                class Args:
                    def __init__(self):
                        self.transport = "webrtc"
                        self.host = "0.0.0.0"
                        self.port = 7860
                
                args = Args()
                
                # 使用异步任务启动DeepSeek AI机器人
                asyncio.create_task(run_deepseek_voice_bot(transport, args, False))
            
            answer = pipecat_connection.get_answer()
            pcs_map[answer["pc_id"]] = pipecat_connection
            
            logger.info(f"✅ WebRTC连接已建立: {answer['pc_id']}")
            return answer
            
        except Exception as e:
            logger.error(f"❌ WebRTC连接失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            raise
    
    return app


def main():
    """主函数"""
    logger.info("🚀 启动DeepSeek版中文语音对话机器人")
    
    # 检查API密钥
    if not os.getenv("DEEPSEEK_API_KEY"):
        logger.error("❌ 缺少DEEPSEEK_API_KEY环境变量")
        logger.error("请在.env文件中设置: DEEPSEEK_API_KEY=your_api_key")
        sys.exit(1)
    
    if not os.getenv("GROQ_API_KEY"):
        logger.error("❌ 缺少GROQ_API_KEY环境变量")
        logger.error("请在.env文件中设置: GROQ_API_KEY=your_api_key")
        sys.exit(1)
    
    logger.info("✅ DeepSeek和Groq API密钥检查通过")
    
    # 检查SSL证书
    ssl_certfile = "/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem"
    ssl_keyfile = "/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem"
    
    if not os.path.exists(ssl_certfile) or not os.path.exists(ssl_keyfile):
        logger.error("❌ SSL证书文件不存在")
        logger.error(f"证书文件: {ssl_certfile}")
        logger.error(f"密钥文件: {ssl_keyfile}")
        sys.exit(1)
    
    logger.info("✅ SSL证书检查通过")
    
    # 创建FastAPI应用
    app = create_deepseek_app()
    
    # 启动HTTPS服务器
    logger.info("🔒 启动DeepSeek版HTTPS WebRTC服务器")
    logger.info("📱 访问地址: https://su.guiyunai.fun:7860")
    logger.info("🎯 功能: STT(Groq) → LLM(DeepSeek) → TTS(Groq)")
    logger.info("🗣️ 支持: 中文语音识别、DeepSeek AI对话、语音合成")
    logger.info("⚡ 优化: 低延迟VAD设置，快速响应")
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=7860,
            ssl_keyfile=ssl_keyfile,
            ssl_certfile=ssl_certfile,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器运行错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
