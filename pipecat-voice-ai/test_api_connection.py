#!/usr/bin/env python3

"""
测试API连接和DeepSeek集成
"""

import asyncio
import os
import aiohttp
import ssl
from dotenv import load_dotenv

load_dotenv()

async def test_deepseek_api():
    """测试DeepSeek API连接"""
    print("🔍 测试DeepSeek API连接...")
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ 缺少DEEPSEEK_API_KEY")
        return False
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "user", "content": "你好，请用中文回答：今天天气怎么样？"}
        ],
        "max_tokens": 100
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://api.deepseek.com/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    print(f"✅ DeepSeek API响应成功")
                    print(f"📝 回复内容: {content[:100]}...")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ DeepSeek API错误: {response.status}")
                    print(f"错误详情: {error_text[:200]}...")
                    return False
    except Exception as e:
        print(f"❌ DeepSeek API连接失败: {e}")
        return False

async def test_groq_api():
    """测试Groq API连接"""
    print("\n🔍 测试Groq API连接...")
    
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        print("❌ 缺少GROQ_API_KEY")
        return False
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试STT端点
    print("📝 测试Groq STT (Whisper)...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(
                "https://api.groq.com/openai/v1/models",
                headers=headers,
                timeout=10
            ) as response:
                if response.status == 200:
                    models = await response.json()
                    whisper_models = [m for m in models.get("data", []) if "whisper" in m.get("id", "").lower()]
                    if whisper_models:
                        print(f"✅ 找到 {len(whisper_models)} 个Whisper模型")
                        for model in whisper_models[:2]:
                            print(f"   - {model.get('id')}")
                    else:
                        print("⚠️ 未找到Whisper模型")
                else:
                    print(f"❌ Groq模型列表获取失败: {response.status}")
    except Exception as e:
        print(f"❌ Groq STT测试失败: {e}")
    
    # 测试LLM端点
    print("🤖 测试Groq LLM...")
    data = {
        "model": "llama3-8b-8192",
        "messages": [
            {"role": "user", "content": "Hello, respond in Chinese: 你好吗？"}
        ],
        "max_tokens": 50
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://api.groq.com/openai/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    print(f"✅ Groq LLM响应成功")
                    print(f"📝 回复内容: {content[:100]}...")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Groq LLM错误: {response.status}")
                    print(f"错误详情: {error_text[:200]}...")
                    return False
    except Exception as e:
        print(f"❌ Groq LLM连接失败: {e}")
        return False

async def test_webrtc_offer():
    """测试WebRTC offer端点"""
    print("\n🔍 测试WebRTC offer端点...")
    
    # 创建SSL上下文，忽略证书验证
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    
    connector = aiohttp.TCPConnector(ssl=ssl_context)
    
    # 模拟真实的WebRTC offer数据
    offer_data = {
        "pc_id": "test-connection-123",
        "type": "offer",
        "sdp": "v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0\r\na=msid-semantic: WMS\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:test\r\na=ice-pwd:testpassword\r\na=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00\r\na=setup:actpass\r\na=mid:0\r\na=sendrecv\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\n"
    }
    
    try:
        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.post(
                "https://su.guiyunai.fun:7860/api/offer",
                json=offer_data,
                timeout=30
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ WebRTC offer成功")
                    print(f"📡 返回数据: {list(result.keys())}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ WebRTC offer失败: {response.status}")
                    print(f"错误详情: {error_text[:300]}...")
                    return False
    except Exception as e:
        print(f"❌ WebRTC offer连接失败: {e}")
        return False

async def test_component_initialization():
    """测试组件初始化"""
    print("\n🔍 测试Pipecat组件初始化...")
    
    try:
        # 测试STT服务初始化
        from pipecat.services.openai.stt import OpenAISTTService
        from pipecat.transcriptions.language import Language
        
        stt = OpenAISTTService(
            api_key=os.getenv("GROQ_API_KEY"),
            base_url="https://api.groq.com/openai/v1",
            model="whisper-large-v3",
            language=Language.ZH,
        )
        print("✅ STT服务初始化成功")
        
        # 测试LLM服务初始化
        from pipecat.services.openai.llm import OpenAILLMService
        
        llm = OpenAILLMService(
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            base_url="https://api.deepseek.com",
            model="deepseek-chat",
        )
        print("✅ LLM服务初始化成功")
        
        # 测试TTS服务初始化
        try:
            from pipecat.services.groq.tts import GroqTTSService
            tts = GroqTTSService(
                api_key=os.getenv("GROQ_API_KEY"),
                voice="alloy"
            )
            print("✅ Groq TTS服务初始化成功")
        except ImportError:
            from pipecat.services.openai.tts import OpenAITTSService
            openai_key = os.getenv("OPENAI_API_KEY")
            if openai_key and openai_key.strip():
                tts = OpenAITTSService(
                    api_key=openai_key,
                    voice="alloy",
                    model="tts-1",
                )
                print("✅ OpenAI TTS服务初始化成功")
            else:
                print("⚠️ 没有可用的TTS服务")
        
        return True
        
    except Exception as e:
        print(f"❌ 组件初始化失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始全面测试pipecat项目...")
    print("=" * 50)
    
    results = []
    
    # 测试DeepSeek API
    results.append(await test_deepseek_api())
    
    # 测试Groq API
    results.append(await test_groq_api())
    
    # 测试组件初始化
    results.append(await test_component_initialization())
    
    # 测试WebRTC端点
    results.append(await test_webrtc_offer())
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"✅ 成功: {sum(results)}/{len(results)}")
    print(f"❌ 失败: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有测试通过！项目部署正常")
    else:
        print("⚠️ 部分测试失败，需要检查配置")
    
    return all(results)

if __name__ == "__main__":
    asyncio.run(main())
