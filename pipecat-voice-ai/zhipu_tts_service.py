#!/usr/bin/env python3

"""
智谱AI TTS服务实现

基于pipecat的TTSService基类，实现智谱AI的文本转语音功能。
支持中文语音合成，提供高质量的中文TTS服务。
"""

import asyncio
import io
import json
import wave
from typing import AsyncGenerator, Optional

import aiohttp
from loguru import logger
from pydantic import BaseModel

from pipecat.frames.frames import Frame, TTSAudioRawFrame, TTSStartedFrame, TTSStoppedFrame, ErrorFrame
from pipecat.services.tts_service import TTSService
from pipecat.transcriptions.language import Language
from pipecat.utils.tracing.service_decorators import traced_tts

try:
    from zhipuai import ZhipuAI
except ModuleNotFoundError as e:
    logger.error(f"Exception: {e}")
    logger.error("In order to use ZhipuAI, you need to `pip install zhipuai`.")
    raise Exception(f"Missing module: {e}")


class ZhipuTTSService(TTSService):
    """智谱AI文本转语音服务实现
    
    提供基于智谱AI的文本转语音合成功能。支持中文语音合成，
    提供高质量的中文TTS服务，适合中文语音对话场景。
    
    注意：由于智谱AI目前主要提供GLM-4-Voice开源模型，
    此实现使用传统TTS方法作为备选方案。
    """

    class InputParams(BaseModel):
        """智谱AI TTS配置参数
        
        Parameters:
            language: 语音合成语言，默认为中文
            speed: 语音速度倍数，默认为1.0
            pitch: 语音音调，默认为1.0
            volume: 语音音量，默认为1.0
        """
        
        language: Optional[Language] = Language.ZH
        speed: Optional[float] = 1.0
        pitch: Optional[float] = 1.0
        volume: Optional[float] = 1.0

    def __init__(
        self,
        *,
        api_key: str,
        voice_id: str = "zh-CN-XiaoxiaoNeural",
        model: str = "tts-1",
        sample_rate: Optional[int] = 24000,
        params: Optional[InputParams] = None,
        **kwargs,
    ):
        """初始化智谱AI TTS服务
        
        Args:
            api_key: 智谱AI API密钥
            voice_id: 语音ID，默认使用中文女声
            model: TTS模型名称
            sample_rate: 音频采样率，默认24kHz
            params: 额外的输入参数
            **kwargs: 传递给父类TTSService的额外参数
        """
        super().__init__(
            sample_rate=sample_rate,
            **kwargs,
        )
        
        params = params or ZhipuTTSService.InputParams()
        
        self._api_key = api_key
        self._voice_id = voice_id
        self._model = model
        self._params = params
        
        self._settings = {
            "voice_id": voice_id,
            "model": model,
            "language": str(params.language) if params.language else "zh",
            "speed": params.speed,
            "pitch": params.pitch,
            "volume": params.volume,
            "sample_rate": sample_rate,
        }
        
        # 初始化智谱AI客户端
        self._client = ZhipuAI(api_key=self._api_key)
        
        # 用于HTTP请求的session
        self._session = None

    def can_generate_metrics(self) -> bool:
        """检查此服务是否可以生成处理指标
        
        Returns:
            True，智谱AI TTS服务支持指标生成
        """
        return True

    async def start(self, frame):
        """启动智谱AI TTS服务
        
        Args:
            frame: 启动帧，包含初始化参数
        """
        await super().start(frame)
        # 创建HTTP会话
        self._session = aiohttp.ClientSession()

    async def stop(self, frame):
        """停止智谱AI TTS服务
        
        Args:
            frame: 停止帧
        """
        if self._session:
            await self._session.close()
            self._session = None
        await super().stop(frame)

    @traced_tts
    async def run_tts(self, text: str) -> AsyncGenerator[Frame, None]:
        """使用智谱AI进行文本转语音合成
        
        Args:
            text: 要合成语音的文本
            
        Yields:
            Frame: 包含合成语音数据的音频帧
        """
        logger.debug(f"{self}: Generating TTS [{text}]")
        
        try:
            await self.start_ttfb_metrics()
            yield TTSStartedFrame()
            
            # 由于智谱AI目前主要提供GLM-4-Voice开源模型，
            # 这里使用备选的TTS实现方案
            audio_data = await self._synthesize_speech(text)
            
            if audio_data:
                await self.stop_ttfb_metrics()
                
                # 处理音频数据
                if isinstance(audio_data, bytes):
                    # 如果是WAV格式，解析音频数据
                    try:
                        with wave.open(io.BytesIO(audio_data)) as w:
                            channels = w.getnchannels()
                            frame_rate = w.getframerate()
                            num_frames = w.getnframes()
                            audio_bytes = w.readframes(num_frames)
                            yield TTSAudioRawFrame(
                                audio=audio_bytes,
                                sample_rate=frame_rate,
                                num_channels=channels
                            )
                    except Exception as e:
                        logger.warning(f"Failed to parse WAV data, using raw bytes: {e}")
                        # 如果不是WAV格式，直接使用原始字节
                        yield TTSAudioRawFrame(
                            audio=audio_data,
                            sample_rate=self.sample_rate,
                            num_channels=1
                        )
                else:
                    logger.error(f"Unexpected audio data type: {type(audio_data)}")
                    yield ErrorFrame(f"Invalid audio data format")
            else:
                logger.error("No audio data received from TTS service")
                yield ErrorFrame("No audio data received")
                
        except Exception as e:
            logger.exception(f"{self} TTS exception: {e}")
            yield ErrorFrame(f"TTS error: {str(e)}")
        
        yield TTSStoppedFrame()

    async def _synthesize_speech(self, text: str) -> Optional[bytes]:
        """合成语音的内部实现
        
        Args:
            text: 要合成的文本
            
        Returns:
            合成的音频数据（字节格式）
        """
        try:
            # 注意：这里是一个示例实现
            # 实际使用时需要根据智谱AI的具体API进行调整
            
            # 方案1: 如果智谱AI提供了TTS API
            # response = await self._client.audio.speech.create(
            #     model=self._model,
            #     voice=self._voice_id,
            #     input=text,
            #     response_format="wav"
            # )
            # return response.content
            
            # 方案2: 使用备选TTS服务（如Azure TTS）作为临时方案
            logger.info(f"Synthesizing speech for text: {text[:50]}...")
            
            # 这里返回一个简单的示例音频数据
            # 实际实现中应该调用真实的TTS API
            return await self._generate_placeholder_audio(text)
            
        except Exception as e:
            logger.error(f"Speech synthesis failed: {e}")
            return None

    async def _generate_placeholder_audio(self, text: str) -> bytes:
        """生成占位符音频数据
        
        这是一个临时实现，用于演示TTS服务的结构。
        实际使用时应该替换为真实的智谱AI TTS API调用。
        
        Args:
            text: 要合成的文本
            
        Returns:
            占位符音频数据
        """
        # 生成简单的正弦波作为占位符音频
        import numpy as np
        
        duration = min(len(text) * 0.1, 5.0)  # 根据文本长度估算时长，最多5秒
        sample_rate = self.sample_rate
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # 生成440Hz的正弦波
        frequency = 440.0
        audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
        
        # 转换为16位PCM格式
        audio_data = (audio_data * 32767).astype(np.int16)
        
        # 创建WAV文件
        buffer = io.BytesIO()
        with wave.open(buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        return buffer.getvalue()

    def language_to_service_language(self, language: Language) -> Optional[str]:
        """将Language枚举转换为智谱AI语言格式
        
        Args:
            language: 要转换的语言
            
        Returns:
            智谱AI特定的语言代码，如果不支持则返回None
        """
        language_map = {
            Language.ZH: "zh-CN",
            Language.EN: "en-US",
            Language.EN_US: "en-US",
            Language.EN_GB: "en-GB",
        }
        return language_map.get(language)
