2025-08-01 16:54:27.735 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-08-01 16:54:32.189 | INFO     | __main__:main:191 - 🚀 启动HTTPS版本的中文语音对话机器人
2025-08-01 16:54:32.189 | INFO     | __main__:main:203 - ✅ SSL证书检查通过
2025-08-01 16:54:32.202 | INFO     | __main__:main:209 - 🔒 启动HTTPS WebRTC服务器
2025-08-01 16:54:32.203 | INFO     | __main__:main:210 - 📱 访问地址: https://su.guiyunai.fun:7860
2025-08-01 16:54:32.203 | INFO     | __main__:main:211 - ⚠️  请确保防火墙开放7860端口
INFO:     Started server process [99562]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:8081 (Press CTRL+C to quit)
Looking for dist directory at: /www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/pipecat_ai_small_webrtc_prebuilt/client/dist
INFO:     *************:0 - "GET /client/ HTTP/1.1" 304 Not Modified
INFO:     *************:0 - "GET /client/assets/index-CJ8kfZwS.js HTTP/1.1" 304 Not Modified
INFO:     *************:0 - "GET /client/assets/index-CdJgXBqL.css HTTP/1.1" 304 Not Modified
INFO:     **************:0 - "GET /client/ HTTP/1.1" 200 OK
INFO:     **************:0 - "GET /client/assets/index-CJ8kfZwS.js HTTP/1.1" 200 OK
INFO:     **************:0 - "GET /client/assets/index-CdJgXBqL.css HTTP/1.1" 200 OK
INFO:     **************:0 - "GET /client/favicon.svg HTTP/1.1" 200 OK
2025-08-01 16:56:48.533 | DEBUG    | pipecat.transports.network.webrtc_connection:_initialize:231 - Initializing new peer connection
2025-08-01 16:56:48.550 | DEBUG    | pipecat.transports.network.webrtc_connection:_create_answer:317 - Creating answer
2025-08-01 16:56:48.551 | DEBUG    | pipecat.transports.network.webrtc_connection:on_track:299 - Track audio received
2025-08-01 16:56:48.552 | DEBUG    | pipecat.transports.network.webrtc_connection:on_track:299 - Track video received
2025-08-01 16:56:48.554 | DEBUG    | pipecat.transports.network.webrtc_connection:on_icegatheringstatechange:295 - ICE gathering state is gathering
2025-08-01 16:56:48.556 | DEBUG    | pipecat.transports.network.webrtc_connection:on_icegatheringstatechange:295 - ICE gathering state is complete
2025-08-01 16:56:48.557 | DEBUG    | pipecat.transports.network.webrtc_connection:_create_answer:320 - Setting the answer after the local description is created
2025-08-01 16:56:48.558 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-08-01 16:56:48.791 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
INFO:     **************:0 - "POST /api/offer HTTP/1.1" 200 OK
2025-08-01 16:56:48.793 | INFO     | __main__:run_voice_bot:41 - 🤖 启动中文语音对话机器人
2025-08-01 16:56:49.016 | INFO     | __main__:run_voice_bot:63 - 🎵 使用智谱AI TTS服务
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-08-01 16:56:49.104 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> SmallWebRTCInputTransport#0
2025-08-01 16:56:49.105 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking SmallWebRTCInputTransport#0 -> OpenAISTTService#0
2025-08-01 16:56:49.105 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAISTTService#0 -> OpenAIUserContextAggregator#0
2025-08-01 16:56:49.105 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> OpenAILLMService#0
2025-08-01 16:56:49.105 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAILLMService#0 -> ZhipuTTSService#0
2025-08-01 16:56:49.105 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking ZhipuTTSService#0 -> SmallWebRTCOutputTransport#0
2025-08-01 16:56:49.105 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking SmallWebRTCOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-08-01 16:56:49.106 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-08-01 16:56:49.106 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-08-01 16:56:49.106 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-08-01 16:56:49.107 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-08-01 16:56:49.111 | INFO     | pipecat.transports.network.small_webrtc:connect:405 - Connecting to Small WebRTC
2025-08-01 16:56:49.112 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-08-01 16:56:49.113 | INFO     | pipecat.transports.network.small_webrtc:connect:405 - Connecting to Small WebRTC
2025-08-01 16:56:49.114 | DEBUG    | pipecat.transports.network.webrtc_connection:on_iceconnectionstatechange:289 - ICE connection state is checking, connection is connecting
2025-08-01 16:56:49.114 | DEBUG    | pipecat.transports.network.webrtc_connection:_handle_new_connection_state:454 - Connection state changed to: connecting
2025-08-01 16:56:49.298 | DEBUG    | pipecat.transports.network.webrtc_connection:on_iceconnectionstatechange:289 - ICE connection state is completed, connection is connecting
2025-08-01 16:56:49.444 | DEBUG    | pipecat.transports.network.webrtc_connection:_handle_new_connection_state:454 - Connection state changed to: connected
2025-08-01 16:56:49.466 | DEBUG    | pipecat.transports.network.small_webrtc:on_connected:236 - Peer connection established.
2025-08-01 16:56:49.467 | DEBUG    | pipecat.transports.network.webrtc_connection:replace_audio_track:393 - Replacing audio track audio
2025-08-01 16:56:49.467 | INFO     | __main__:on_client_connected:109 - 客户端已连接: SmallWebRTCConnection#0
2025-08-01 16:56:49.470 | DEBUG    | zhipu_tts_service:run_tts:145 - ZhipuTTSService#0: Generating TTS [你好！我是您的中文语音助手。请开始说话，我会用语音回答您的问题。]
2025-08-01 16:56:49.471 | INFO     | zhipu_tts_service:_synthesize_speech:216 - Synthesizing speech for text: 你好！我是您的中文语音助手。请开始说话，我会用语音回答您的问题。...
2025-08-01 16:56:49.477 | INFO     | pipecat.transports.network.small_webrtc:disconnect:411 - Disconnecting to Small WebRTC
2025-08-01 16:56:49.478 | DEBUG    | pipecat.transports.network.webrtc_connection:send_app_message:535 - Data channel not ready, queuing message
2025-08-01 16:56:49.481 | DEBUG    | pipecat.transports.base_output:_bot_started_speaking:564 - Bot started speaking
2025-08-01 16:56:49.531 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
2025-08-01 16:56:50.560 | DEBUG    | pipecat.transports.network.webrtc_connection:_handle_signalling_message:550 - Signalling message received: {'type': 'trackStatus', 'receiver_index': 0, 'enabled': True}
2025-08-01 16:56:50.561 | DEBUG    | pipecat.transports.network.webrtc_connection:_handle_signalling_message:550 - Signalling message received: {'type': 'trackStatus', 'receiver_index': 1, 'enabled': True}
2025-08-01 16:56:50.565 | DEBUG    | pipecat.transports.network.small_webrtc:push_app_message:627 - Received app message inside SmallWebRTCInputTransport  {'label': 'rtvi-ai', 'type': 'client-ready', 'data': {'version': '1.0.0', 'about': {'library': '@pipecat-ai/client-react', 'library_version': '1.0.1', 'platform_details': {'browser': 'Safari', 'browser_version': '17.6', 'platform_type': 'mobile', 'engine': 'WebKit'}, 'platform': 'iOS', 'platform_version': '17.6.1'}}, 'id': '9672077c'}
INFO:     ***************:0 - "GET /client/ HTTP/1.1" 200 OK
2025-08-01 16:57:21.175 | DEBUG    | pipecat.transports.network.webrtc_connection:_initialize:231 - Initializing new peer connection
INFO:     ***************:0 - "POST /api/offer HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/errors.py", line 187, in __call__
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/voice_bot_https.py", line 153, in offer
    await pipecat_connection.initialize(sdp=request["sdp"], type=request["type"])
                                            ~~~~~~~^^^^^^^
KeyError: 'sdp'
INFO:     ***************:0 - "GET /client/ HTTP/1.1" 200 OK
2025-08-01 16:59:34.233 | DEBUG    | pipecat.transports.network.webrtc_connection:_initialize:231 - Initializing new peer connection
2025-08-01 16:59:34.241 | DEBUG    | pipecat.transports.network.webrtc_connection:_create_answer:317 - Creating answer
2025-08-01 16:59:34.243 | DEBUG    | pipecat.transports.network.webrtc_connection:on_track:299 - Track audio received
2025-08-01 16:59:34.243 | DEBUG    | pipecat.transports.network.webrtc_connection:on_track:299 - Track video received
2025-08-01 16:59:34.244 | DEBUG    | pipecat.transports.network.webrtc_connection:on_icegatheringstatechange:295 - ICE gathering state is gathering
2025-08-01 16:59:34.246 | DEBUG    | pipecat.transports.network.webrtc_connection:on_icegatheringstatechange:295 - ICE gathering state is complete
2025-08-01 16:59:34.246 | DEBUG    | pipecat.transports.network.webrtc_connection:_create_answer:320 - Setting the answer after the local description is created
2025-08-01 16:59:34.247 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-08-01 16:59:34.414 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
INFO:     *************:0 - "POST /api/offer HTTP/1.1" 200 OK
2025-08-01 16:59:34.416 | INFO     | __main__:run_voice_bot:41 - 🤖 启动中文语音对话机器人
2025-08-01 16:59:34.542 | INFO     | __main__:run_voice_bot:63 - 🎵 使用智谱AI TTS服务
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-08-01 16:59:34.613 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#1 -> SmallWebRTCInputTransport#1
2025-08-01 16:59:34.613 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking SmallWebRTCInputTransport#1 -> OpenAISTTService#1
2025-08-01 16:59:34.614 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAISTTService#1 -> OpenAIUserContextAggregator#1
2025-08-01 16:59:34.614 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#1 -> OpenAILLMService#1
2025-08-01 16:59:34.614 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAILLMService#1 -> ZhipuTTSService#1
2025-08-01 16:59:34.614 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking ZhipuTTSService#1 -> SmallWebRTCOutputTransport#1
2025-08-01 16:59:34.614 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking SmallWebRTCOutputTransport#1 -> OpenAIAssistantContextAggregator#1
2025-08-01 16:59:34.614 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#1 -> PipelineSink#1
2025-08-01 16:59:34.614 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#1 -> Pipeline#1
2025-08-01 16:59:34.615 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#1 -> PipelineTaskSink#1
2025-08-01 16:59:34.615 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#1 started running PipelineTask#1
2025-08-01 16:59:34.618 | INFO     | pipecat.transports.network.small_webrtc:connect:405 - Connecting to Small WebRTC
2025-08-01 16:59:34.618 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-08-01 16:59:34.619 | INFO     | pipecat.transports.network.small_webrtc:connect:405 - Connecting to Small WebRTC
2025-08-01 16:59:34.621 | DEBUG    | pipecat.transports.network.webrtc_connection:on_iceconnectionstatechange:289 - ICE connection state is checking, connection is connecting
2025-08-01 16:59:34.621 | DEBUG    | pipecat.transports.network.webrtc_connection:_handle_new_connection_state:454 - Connection state changed to: connecting
2025-08-01 16:59:34.785 | DEBUG    | pipecat.transports.network.webrtc_connection:on_iceconnectionstatechange:289 - ICE connection state is completed, connection is connecting
2025-08-01 16:59:34.902 | DEBUG    | pipecat.transports.network.webrtc_connection:_handle_new_connection_state:454 - Connection state changed to: connected
2025-08-01 16:59:34.903 | DEBUG    | pipecat.transports.network.small_webrtc:on_connected:236 - Peer connection established.
2025-08-01 16:59:34.903 | DEBUG    | pipecat.transports.network.webrtc_connection:replace_audio_track:393 - Replacing audio track audio
2025-08-01 16:59:34.903 | INFO     | __main__:on_client_connected:109 - 客户端已连接: SmallWebRTCConnection#2
2025-08-01 16:59:34.906 | DEBUG    | zhipu_tts_service:run_tts:145 - ZhipuTTSService#1: Generating TTS [你好！我是您的中文语音助手。请开始说话，我会用语音回答您的问题。]
2025-08-01 16:59:34.906 | INFO     | zhipu_tts_service:_synthesize_speech:216 - Synthesizing speech for text: 你好！我是您的中文语音助手。请开始说话，我会用语音回答您的问题。...
2025-08-01 16:59:34.910 | INFO     | pipecat.transports.network.small_webrtc:disconnect:411 - Disconnecting to Small WebRTC
2025-08-01 16:59:34.911 | DEBUG    | pipecat.transports.network.webrtc_connection:send_app_message:535 - Data channel not ready, queuing message
2025-08-01 16:59:34.913 | DEBUG    | pipecat.transports.base_output:_bot_started_speaking:564 - Bot started speaking
2025-08-01 16:59:34.949 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#1 finished running PipelineTask#1
2025-08-01 16:59:35.011 | DEBUG    | pipecat.transports.network.webrtc_connection:_handle_signalling_message:550 - Signalling message received: {'type': 'trackStatus', 'receiver_index': 0, 'enabled': True}
2025-08-01 16:59:35.012 | DEBUG    | pipecat.transports.network.webrtc_connection:_handle_signalling_message:550 - Signalling message received: {'type': 'trackStatus', 'receiver_index': 1, 'enabled': False}
2025-08-01 16:59:35.012 | DEBUG    | pipecat.transports.network.small_webrtc:push_app_message:627 - Received app message inside SmallWebRTCInputTransport  {'label': 'rtvi-ai', 'type': 'client-ready', 'data': {'version': '1.0.0', 'about': {'library': '@pipecat-ai/client-react', 'library_version': '1.0.1', 'platform_details': {'browser': 'Chrome', 'browser_version': '*********', 'platform_type': 'desktop', 'engine': 'Blink'}, 'platform': 'Windows', 'platform_version': 'NT 10.0'}}, 'id': '4aeaf06f'}
INFO:     **************:0 - "POST /api/offer HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/errors.py", line 187, in __call__
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/voice_bot_https.py", line 145, in offer
    await pipecat_connection.restart(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SmallWebRTCConnection' object has no attribute 'restart'
INFO:     **************:0 - "POST /api/offer HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/errors.py", line 187, in __call__
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/voice_bot_https.py", line 145, in offer
    await pipecat_connection.restart(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SmallWebRTCConnection' object has no attribute 'restart'
INFO:     **************:0 - "POST /api/offer HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/errors.py", line 187, in __call__
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/voice_bot_https.py", line 145, in offer
    await pipecat_connection.restart(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SmallWebRTCConnection' object has no attribute 'restart'
2025-08-01 17:00:18.376 | DEBUG    | pipecat.transports.network.webrtc_connection:_handle_new_connection_state:454 - Connection state changed to: closed
2025-08-01 17:00:18.377 | INFO     | __main__:handle_disconnected:157 - WebRTC连接已关闭: SmallWebRTCConnection#0
2025-08-01 17:00:18.378 | DEBUG    | pipecat.transports.network.small_webrtc:on_closed:246 - Client connection closed.
2025-08-01 17:00:18.378 | ERROR    | pipecat.utils.base_object:_run_task:157 - Exception in event handler on_client_disconnected: run_voice_bot.<locals>.on_client_disconnected() missing 1 required positional argument: 'reason'
Traceback (most recent call last):

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/voice_bot_https.py", line 228, in <module>
    main()
    └ <function main at 0x791e82871e40>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/voice_bot_https.py", line 214, in main
    uvicorn.run(
    │       └ <function run at 0x791ea1d9c360>
    └ <module 'uvicorn' from '/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/__init__.py'>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x791ea1df9940>
    └ <uvicorn.server.Server object at 0x791e7c559400>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x791ea1df99e0>
           │       │   └ <uvicorn.server.Server object at 0x791e7c559400>
           │       └ <function run at 0x791ea2919bc0>
           └ <module 'asyncio' from '/usr/lib/python3.12/asyncio/__init__.py'>
  File "/usr/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x791e7c5692a0>
           │      └ <function Runner.run at 0x791ea1f36980>
           └ <asyncio.runners.Runner object at 0x791e7c9361e0>
  File "/usr/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/...
           │    │     └ <cyfunction Loop.run_until_complete at 0x791e7c5ada40>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x791e7c9361e0>
> File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/pipecat/utils/base_object.py", line 153, in _run_task
    await handler(self, *args, **kwargs)
          │       │      │       └ {}
          │       │      └ (<pipecat.transports.network.webrtc_connection.SmallWebRTCConnection object at 0x791e7c54c2f0>,)
          │       └ <pipecat.transports.network.small_webrtc.SmallWebRTCTransport object at 0x791e7c5a2ff0>
          └ <function run_voice_bot.<locals>.on_client_disconnected at 0x791e7a7bb380>

TypeError: run_voice_bot.<locals>.on_client_disconnected() missing 1 required positional argument: 'reason'
2025-08-01 17:00:18.384 | DEBUG    | pipecat.transports.network.webrtc_connection:on_iceconnectionstatechange:289 - ICE connection state is closed, connection is closed
2025-08-01 17:00:41.099 | DEBUG    | pipecat.transports.network.webrtc_connection:_handle_new_connection_state:454 - Connection state changed to: closed
2025-08-01 17:00:41.100 | INFO     | __main__:handle_disconnected:157 - WebRTC连接已关闭: SmallWebRTCConnection#2
2025-08-01 17:00:41.100 | DEBUG    | pipecat.transports.network.small_webrtc:on_closed:246 - Client connection closed.
2025-08-01 17:00:41.100 | ERROR    | pipecat.utils.base_object:_run_task:157 - Exception in event handler on_client_disconnected: run_voice_bot.<locals>.on_client_disconnected() missing 1 required positional argument: 'reason'
Traceback (most recent call last):

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/voice_bot_https.py", line 228, in <module>
    main()
    └ <function main at 0x791e82871e40>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/voice_bot_https.py", line 214, in main
    uvicorn.run(
    │       └ <function run at 0x791ea1d9c360>
    └ <module 'uvicorn' from '/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/__init__.py'>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x791ea1df9940>
    └ <uvicorn.server.Server object at 0x791e7c559400>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x791ea1df99e0>
           │       │   └ <uvicorn.server.Server object at 0x791e7c559400>
           │       └ <function run at 0x791ea2919bc0>
           └ <module 'asyncio' from '/usr/lib/python3.12/asyncio/__init__.py'>
  File "/usr/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x791e7c5692a0>
           │      └ <function Runner.run at 0x791ea1f36980>
           └ <asyncio.runners.Runner object at 0x791e7c9361e0>
  File "/usr/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/...
           │    │     └ <cyfunction Loop.run_until_complete at 0x791e7c5ada40>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x791e7c9361e0>
> File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/env/lib/python3.12/site-packages/pipecat/utils/base_object.py", line 153, in _run_task
    await handler(self, *args, **kwargs)
          │       │      │       └ {}
          │       │      └ (<pipecat.transports.network.webrtc_connection.SmallWebRTCConnection object at 0x791e7a302ff0>,)
          │       └ <pipecat.transports.network.small_webrtc.SmallWebRTCTransport object at 0x791e7af39820>
          └ <function run_voice_bot.<locals>.on_client_disconnected at 0x791e79f307c0>

TypeError: run_voice_bot.<locals>.on_client_disconnected() missing 1 required positional argument: 'reason'
2025-08-01 17:01:09.521 | DEBUG    | pipecat.transports.network.webrtc_connection:on_iceconnectionstatechange:289 - ICE connection state is closed, connection is closed
