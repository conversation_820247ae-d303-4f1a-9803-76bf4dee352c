#!/bin/bash

# 系统状态检查脚本
# 检查前后端服务、nginx、SSL证书等状态

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查nginx状态
check_nginx() {
    log "检查nginx状态..."
    
    if systemctl is-active --quiet nginx; then
        success "nginx服务正在运行"
    else
        error "nginx服务未运行"
        return 1
    fi
    
    # 检查nginx配置
    if nginx -t &>/dev/null; then
        success "nginx配置文件语法正确"
    else
        error "nginx配置文件有错误"
        return 1
    fi
    
    # 检查端口监听
    if ss -tlnp | grep -q ":80 "; then
        success "nginx监听80端口"
    else
        warning "nginx未监听80端口"
    fi
    
    if ss -tlnp | grep -q ":443 "; then
        success "nginx监听443端口"
    else
        warning "nginx未监听443端口"
    fi
}

# 检查SSL证书
check_ssl() {
    log "检查SSL证书..."
    
    local cert_file="/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem"
    local key_file="/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem"
    
    if [[ -f "$cert_file" && -f "$key_file" ]]; then
        success "SSL证书文件存在"
        
        # 检查证书有效期
        local expiry=$(openssl x509 -enddate -noout -in "$cert_file" | cut -d= -f2)
        local expiry_epoch=$(date -d "$expiry" +%s)
        local current_epoch=$(date +%s)
        local days_left=$(( (expiry_epoch - current_epoch) / 86400 ))
        
        if [[ $days_left -gt 30 ]]; then
            success "SSL证书有效期还有 $days_left 天"
        elif [[ $days_left -gt 0 ]]; then
            warning "SSL证书将在 $days_left 天后过期"
        else
            error "SSL证书已过期"
        fi
    else
        error "SSL证书文件不存在"
        return 1
    fi
}

# 检查后端服务
check_backend() {
    log "检查后端服务..."
    
    local pid_file="/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/voice_bot.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            success "语音机器人后端服务正在运行 (PID: $pid)"
        else
            error "语音机器人后端服务未运行 (PID文件存在但进程不存在)"
            return 1
        fi
    else
        error "语音机器人后端服务未运行 (PID文件不存在)"
        return 1
    fi
    
    # 检查端口监听
    if ss -tlnp | grep -q ":8081 "; then
        success "后端服务监听8081端口"
    else
        error "后端服务未监听8081端口"
        return 1
    fi
}

# 检查环境变量
check_env() {
    log "检查环境变量..."
    
    cd "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai"
    source "env/bin/activate"
    
    if python -c "
import os
from dotenv import load_dotenv
load_dotenv()
groq_key = os.getenv('GROQ_API_KEY')
zhipu_key = os.getenv('ZHIPU_API_KEY')
if groq_key:
    print('GROQ_API_KEY: ✓')
else:
    print('GROQ_API_KEY: ✗')
    exit(1)
if zhipu_key:
    print('ZHIPU_API_KEY: ✓')
else:
    print('ZHIPU_API_KEY: ✗')
    exit(1)
"; then
        success "环境变量配置正确"
    else
        error "环境变量配置有问题"
        return 1
    fi
}

# 检查前端访问
check_frontend() {
    log "检查前端访问..."
    
    # 检查HTTPS访问
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" https://su.guiyunai.fun/ --max-time 10)
    if [[ "$http_code" == "200" ]]; then
        success "前端HTTPS访问正常 (状态码: $http_code)"
    else
        error "前端HTTPS访问异常 (状态码: $http_code)"
        return 1
    fi
    
    # 检查API访问
    local api_code=$(curl -s -o /dev/null -w "%{http_code}" -X POST https://su.guiyunai.fun/api/offer --max-time 10 -H "Content-Type: application/json" -d '{}')
    if [[ "$api_code" == "422" || "$api_code" == "400" ]]; then
        success "API端点可访问 (状态码: $api_code)"
    else
        warning "API端点响应异常 (状态码: $api_code)"
    fi
}

# 检查系统资源
check_resources() {
    log "检查系统资源..."
    
    # 检查内存使用
    local mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    if (( $(echo "$mem_usage < 80" | bc -l) )); then
        success "内存使用率: ${mem_usage}%"
    else
        warning "内存使用率较高: ${mem_usage}%"
    fi
    
    # 检查磁盘使用
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [[ $disk_usage -lt 80 ]]; then
        success "磁盘使用率: ${disk_usage}%"
    else
        warning "磁盘使用率较高: ${disk_usage}%"
    fi
    
    # 检查CPU负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    success "系统负载: $load_avg"
}

# 主检查函数
main() {
    echo "========================================"
    echo "智谱AI语音机器人系统状态检查"
    echo "========================================"
    echo ""
    
    local errors=0
    
    # 执行各项检查
    check_nginx || ((errors++))
    echo ""
    
    check_ssl || ((errors++))
    echo ""
    
    check_backend || ((errors++))
    echo ""
    
    check_env || ((errors++))
    echo ""
    
    check_frontend || ((errors++))
    echo ""
    
    check_resources
    echo ""
    
    # 总结
    echo "========================================"
    if [[ $errors -eq 0 ]]; then
        success "所有检查通过！系统运行正常"
        echo ""
        success "访问地址: https://su.guiyunai.fun/"
        success "管理命令: ./start_voice_bot_service.sh {start|stop|restart|status|logs}"
    else
        error "发现 $errors 个问题，请检查上述错误信息"
        echo ""
        log "故障排除建议:"
        log "1. 检查服务状态: ./start_voice_bot_service.sh status"
        log "2. 查看服务日志: ./start_voice_bot_service.sh logs"
        log "3. 重启服务: ./start_voice_bot_service.sh restart"
        log "4. 检查nginx: systemctl status nginx"
    fi
    echo "========================================"
    
    exit $errors
}

# 执行主函数
main "$@"
